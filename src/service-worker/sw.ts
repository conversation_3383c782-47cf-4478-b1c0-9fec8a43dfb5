/* eslint-disable no-restricted-globals,@typescript-eslint/ban-ts-comment */
import MD5 from 'crypto-js/md5';
import * as idbKeyVal from 'idb-keyval';
import { ExpirationPlugin } from 'workbox-expiration';
import { PrecacheController } from 'workbox-precaching';
import { registerRoute } from 'workbox-routing';
import { StaleWhileRevalidate } from 'workbox-strategies';

declare const self: any;

const CLINIFY_CACHE = 'clinify-sw-cache';
const store = idbKeyVal.createStore('Clinify-API-Cache', 'PostResponses');

const precacheController = new PrecacheController({
  cacheName: CLINIFY_CACHE,
  plugins: [new ExpirationPlugin({ maxAgeSeconds: 86400, maxEntries: 200 })],
});

registerRoute(
  new RegExp('/graphql(/)?'),
  // @ts-ignore
  async ({ event }) => {
    return networkFirst(event);
  },
  'POST',
);

precacheController.addToCacheList([
  { url: '/index.html', revision: '001' },
  { url: '/manifest.json', revision: '001' },
  { url: '/css/bootstrap.css', revision: null },
  { url: '/css/mdb.min.css', revision: null },
  { url: '/css/styles.css', revision: '001' },
  { url: '/fonts/Lato-Bold.ttf', revision: null },
  { url: '/fonts/Lato-Regular.ttf', revision: null },
  { url: '/images/camera.png', revision: null },
  { url: '/images/profile-image.png', revision: null },
  { url: '/js/bootstrap.js', revision: null },
  { url: '/js/jquery-3.4.1.min.js', revision: null },
  { url: '/js/popper.min.js', revision: null },
  { url: '/icons/manifest-icon-192.png', revision: '001' },
  { url: '/icons/manifest-icon-512.png', revision: '001' },
  {
    url: 'https://fonts.googleapis.com/css2?family=Lato:wght@300;400;500;600;700&display=swap',
    revision: null,
  },
  {
    url: 'https://fonts.gstatic.com/s/poppins/v15/pxiByp8kv8JHgFVrLGT9Z1xlFd2JQEk.woff2',
    revision: null,
  },
  {
    url: 'https://fonts.gstatic.com/s/poppins/v15/pxiEyp8kv8JHgFVrJJfecnFHGPc.woff2',
    revision: null,
  },
  {
    url: 'https://fonts.gstatic.com/s/poppins/v15/pxiByp8kv8JHgFVrLEj6Z1xlFd2JQEk.woff2',
    revision: null,
  },
  { url: '/icons/favicon.ico', revision: null },
]);

async function networkFirst(event) {
  const cachedResponse = await getCache(event.request.clone());
  const fetchPromise = fetch(event.request.clone())
    .then((response) => {
      setCache(event.request.clone(), response.clone());
      if (!response.ok) return cachedResponse || response;
      return response;
    })
    .catch(() => {
      return Promise.resolve(cachedResponse);
    });

  return fetchPromise || Promise.resolve(cachedResponse);
}

async function serializeResponse(response: Response) {
  const serializedHeaders = {};
  // @ts-ignore
  // eslint-disable-next-line no-restricted-syntax
  for (const entry of response.headers.entries()) {
    // eslint-disable-next-line prefer-destructuring
    serializedHeaders[entry[0]] = entry[1];
  }
  const serialized: any = {
    headers: serializedHeaders,
    status: response.status,
    statusText: response.statusText,
  };
  serialized.body = await response.json();

  return serialized;
}

async function setCache(request: Request, response: Response) {
  const body = await request.json();
  const id = MD5(body.query).toString();

  const entry = {
    query: body.query,
    response: await serializeResponse(response),
    timestamp: Date.now(),
  };
  idbKeyVal.set(id, entry, store);
}

async function getCache(request: Request) {
  let data;
  try {
    const body = await request.json();
    const id = MD5(body.query).toString();
    data = await idbKeyVal.get(id, store);
    if (!data) return null;

    const cacheControl = request.headers.get('Cache-Control');
    // eslint-disable-next-line radix
    const maxAge = cacheControl ? parseInt(cacheControl.split('=')[1]) : 3600;
    if (Date.now() - data.timestamp > maxAge * 1000) {
      return null;
    }

    return new Response(JSON.stringify(data.response.body), data.response);
  } catch (err) {
    return null;
  }
}

self.addEventListener('fetch', async (event) => {
  const { request } = event;
  const url = new URL(request.url);

  if (url.origin === location.origin) {
    const cacheKey = precacheController.getCacheKeyForURL(
      `${location.origin}/index.html`,
    ) as string;
    return event.respondWith(
      new StaleWhileRevalidate({ cacheName: CLINIFY_CACHE })
        .handle({ event, request })
        .catch(() => {
          return caches.match(cacheKey);
        }),
    );
  }

  if (!(event.request.url.indexOf('http') === 0) || request.method !== 'GET') return;

  event.respondWith(
    new StaleWhileRevalidate({ cacheName: CLINIFY_CACHE }).handle({ event, request }),
  );
});

// @ts-ignore
self.addEventListener('install', (event: any) => {
  const installProcess = async () => {
    await precacheController.install(event);
    return self.skipWaiting();
  };

  event.waitUntil(installProcess());
});

self.addEventListener('activate', (event) => {
  const activateProcess = async () => {
    await precacheController.activate(event);
    return self.clients.claim();
  };

  event.waitUntil(activateProcess());
});
