import { gql } from '@apollo/client';

export const GET_REGISTERED_HMO_HOSPITALS = gql`
  query GetRegisteredHmoHospitals($filterOptions: HmoFilterOptions!) {
    getRegisteredHmoHospitals(filterOptions: $filterOptions) {
      totalCount
      list {
        id
        name
        plan
        address
        lga
        planStatus
        hmoHospitals {
          id
          enrolleeCount
          enrolleeLimit
        }
      }
    }
  }
`;

export const LEADWAY_PROVIDER_LOGIN = gql`
  mutation LeadwayProviderLogin($input: LeadwayProviderLoginInput!) {
    leadwayProviderLogin(input: $input)
  }
`;

export const CHECK_LEADWAY_ACCESS_TOKEN = gql`
  query CheckLeadwayAccessToken {
    checkLeadwayAccessToken
  }
`;

export const GET_HMO_PROVIDER_BY_CODE = gql`
  query GetHmoProviderByCode($code: String!) {
    getHmoProviderByCode(code: $code) {
      id
      name
      providerCode
      enableWhatsAppNotifications
    }
  }
`;

export const ENABLE_AGENCY_WHATSAPP_NOTIFICATION = gql`
  mutation EnableAgencyWhatsAppNotification($providerCode: String!, $enable: Boolean!) {
    enableWhatsAppNotifications(providerCode: $providerCode, enableWhatsAppNotifications: $enable) {
      id
      enableWhatsAppNotifications
      providerCode
    }
  }
`;

export const FETCH_HMO_PLANS = gql`
  query FetchHmoPlans($providerId: String!, $memberNumber: String!) {
    fetchHmoPlansByProviderId(providerId: $providerId, memberNumber: $memberNumber) {
      id
      name
      isSponsor
      premiumDetails {
        amount
        category
        frequency
        administrationAgency
        enrollmentAgencyCommissionRate {
          name
          commissionRate
          referralCommissionRate
        }
      }
    }
  }
`;

export const UPDATE_HMO_PHARMACY_SUPPORT = gql`
  mutation UpdateHmoPharmacySupport($support: Boolean!, $providers: [String!]!) {
    updateHmoPharmacySupport(support: $support, providers: $providers)
  }
`;

export const GET_HMO_PHARMACY_SUPPORT = gql`
  query GetHmoPharmacySupport {
    getHmoPharmacySupport {
      controlledPriceVisibility
      providersPriceVisibleTo
    }
  }
`;

export const GET_HMO_AGENCY_DETAILS = gql`
  query GetHmoAgency {
    getHmoAgency {
      id
      name
      providerCode
    }
  }
`;

export const GET_HMO_AGENCY_ROLES = gql`
  query GetHmoAgencyRoles($hmoId: String, $providerCode: String) {
    getHmoAgencyRoles(hmoId: $hmoId, providerCode: $providerCode)
  }
`;

export const GET_HMO_HOSPITAL_FLAG = gql`
  query GetHmoHospitalFlag($hospitalId: String!, $providerId: String!) {
    getHmoHospitalByHospitalIdAndProviderId(providerId: $providerId, hospitalId: $hospitalId) {
      id
      flags {
        flag
        ruleId
      }
    }
  }
`;

export const FLAG_HMO_HOSPITAL = gql`
  mutation FlagHmoHospital(
    $hospitalId: String!
    $hmoProviderId: String!
    $flag: String!
    $unset: Boolean!
  ) {
    flagHmoHospitalByHospitalIdAndProviderId(
      hospitalId: $hospitalId
      providerId: $hmoProviderId
      flag: $flag
      unset: $unset
    ) {
      id
      flags {
        flag
        ruleId
      }
    }
  }
`;

export const GET_CAPITATED_ENROLLEES_DATA = gql`
  query GetHmoCapitatedEnrolleesData(
    $hmoPlanTypeId: String
    $hospitalId: String
    $startDate: Date!
    $endDate: Date!
  ) {
    getHmoCapitatedEnrolleesData(
      hmoPlanTypeId: $hmoPlanTypeId
      hospitalId: $hospitalId
      startDate: $startDate
      endDate: $endDate
    ) {
      primaryProviderId
      list {
        profileId
        fullName
        memberStatus
        dateOfBirth
        gender
        phoneNumber
        memberNumber
        emailAddress
        planStartDate
        planEndDate
        planName
        primaryProviderId
        enrolleeCount
        amount
        serviceChargeAmount
        primaryProviderName
        primaryProviderAddress
        narration
      }
    }
  }
`;

export const GET_CAPITATED_ENROLLEES_SUMMARY = gql`
  query GetHmoCapitatedPaymentSummary($hmoPlanTypeId: String, $startDate: Date!, $endDate: Date!) {
    getHmoCapitatedPaymentSummary(
      hmoPlanTypeId: $hmoPlanTypeId
      startDate: $startDate
      endDate: $endDate
    ) {
      totalCapitationAmount
      totalEnrollees
      enrolleeCapitationAmount
      planTypeName
    }
  }
`;

export const SEND_FEE_FOR_SERVICE_PAYMENT_WHATSAPP_NOTIFICATION = gql`
  mutation SendFeeForServicePaymentWhatsAppNotification($claimId: String!) {
    sendFeeForServicePaymentWhatsAppNotification(claimId: $claimId)
  }
`;

export const SEND_CAPITATION_PAYMENT_WHATSAPP_NOTIFICATION = gql`
  mutation SendCapitationPaymentWhatsAppNotification($transferFundId: String!) {
    sendCapitationPaymentWhatsAppNotification(transferFundId: $transferFundId)
  }
`;
