import { gql } from '@apollo/client';
import { CREATED_BY } from 'apollo-queries/fragments/audit';
import { PREAUTHORIZATION_UTILISATIONS as UTILIZATION } from './preauthorizations';

export const HMO_CLAIM = gql`
  fragment HmoClaim on HmoClaimModel {
    id
    claimId
    visitId
    batchNumber
    claimDate
    submitDateTime
    submittedBy
    treatmentStartDate
    treatmentEndDate
    confirmation
    provider {
      id
      name
      providerCode
    }
    serviceType
    serviceTypeCode
    serviceName
    priority
    claimIdentity
    status
    facilityName
    facilityAddress
    presentingComplain
    externalPlanTypeId
    isExternalPlanType
    flags {
      flag
    }
    diagnosis {
      diagnosisICD10
      diagnosisICD11
      diagnosisSNOMED
    }
    utilizations {
      ...PreauthUtilisations
    }
    documentUrl
    totalQuantity
    grandTotal
    additionalNote
    creatorName
    lastModifierName
    updatedDate
    referredBy
    referralCode
    referredFrom
    referredTo
    profileId
    enrolleeNumber
    enrolleePhoneNumber {
      countryCode
      value
      countryName
    }
    revertAction {
      action
      mutatorAlias
      receiverAlias
      mutatorUserType
      receiverUserType
    }
    enrolleeEmail
    profile {
      id
      clinifyId
      fullName
    }
    payoutStatus
    payoutHistory {
      createdDate
      amount
      clinifyId
      payoutId
      utilizationIds
    }
  }
  ${UTILIZATION}
`;

export const FETCH_HMO_CLAIM = gql`
  query HmoClaim($id: String!, $clinifyId: String!) {
    hmoClaim(id: $id, clinifyId: $clinifyId) {
      ...HmoClaim
      enrolleeNumber
      profile {
        id
        clinifyId
        fullName
        gender
        personalInformation {
          dateOfBirth
        }
        secondaryPhoneNumber {
          countryCode
          value
          countryName
        }
        user {
          id
          phoneNumber
          nonCorporateEmail
        }
      }
      ${CREATED_BY}
    }
  }
  ${HMO_CLAIM}
`;

export const GET_HOSPITAL_HMO_CLAIMS = gql`
  query HospitalHmoClaims($filterOptions: HmoClaimFilterInput, $hospitalId: String, $hid: String) {
    hospital(hospitalId: $hospitalId) {
      id
      name
      address
      website
      facebook
      twitter
      instagram
      supportMail
      facilityLogo
      preferredPayoutAccount(hospitalId: $hid) {
        bankName
        accountNumber
        accountName
      }
      hmoClaims(filterOptions: $filterOptions) {
        totalCount
        list {
          ...HmoClaim
          financeApproval {
            createdDate
            creatorName
            creatorId
            approvalGroup
          }
          hospitalId
          utilizations {
            ...PreauthUtilisations
            paUtilProcessed
            flags {
              flag
            }
          }
          profile {
            id
            clinifyId
            fullName
          }
          createdDate
          responseDateTime
          totalSubmittedAmount
          totalRejectedAmount
        }
      }
    }
  }
  ${UTILIZATION}
  ${HMO_CLAIM}
`;

export const GET_USER_HMO_CLAIMS = gql`
  query UserHmoClaims($id: String!, $filterOptions: HmoClaimFilterInput) {
    profile(clinifyId: $id) {
      id
      hmoClaims(filterOptions: $filterOptions) {
        totalCount
        list {
          ...HmoClaim
          utilizations {
            ...PreauthUtilisations
            paUtilProcessed
          }
          createdDate
          responseDateTime
        }
      }
    }
  }
  ${UTILIZATION}
  ${HMO_CLAIM}
`;

export const ADD_HMO_CLAIM = gql`
  mutation AddHmoClaim(
    $input: NewHmoClaimInput!
    $billableInput: BillableClaimInput
    $pin: String
    $autoGenerated: Boolean
    $origin: String!
  ) {
    addHmoClaim(
      input: $input
      billableInput: $billableInput
      pin: $pin
      autoGenerated: $autoGenerated
      origin: $origin
    ) {
      ...HmoClaim
      profile {
        id
        clinifyId
        fullName
      }
    }
  }
  ${HMO_CLAIM}
`;

export const EDIT_HMO_CLAIM = gql`
  mutation EditHmoClaim($input: EditHmoClaimInput!, $origin: String!) {
    editHmoClaim(input: $input, origin: $origin) {
      ...HmoClaim
    }
  }
  ${HMO_CLAIM}
`;

export const ARCHIVE_HMO_CLAIMS = gql`
  mutation ArchiveHmoClaims($ids: [String!]!, $archive: Boolean!) {
    archiveHmoClaims(ids: $ids, archive: $archive) {
      ...HmoClaim
    }
  }
  ${HMO_CLAIM}
`;

export const DELETE_HMO_CLAIMS = gql`
  mutation DeleteHmoClaims($ids: [String!]!) {
    deleteHmoClaims(ids: $ids) {
      id
    }
  }
`;

export const SUBMIT_HMO_CLAIMS = gql`
  mutation SubmitHmoClaims($ids: [String!]!, $origin: String!) {
    submitHmoClaims(ids: $ids, origin: $origin) {
      ...HmoClaim
    }
  }
  ${HMO_CLAIM}
`;

export const SYNC_HMO_CLAIMS = gql`
  mutation SyncHmoClaims($ids: [String!]!) {
    syncHmoClaims(ids: $ids) {
      ...HmoClaim
    }
  }
  ${HMO_CLAIM}
`;

export const FETCH_HOSPITAL_HMO_PAYMENTS = gql`
  query FetchHmoPayments(
    $filterOptions: DateRangeInput
    $hospitalId: String!
    $providerId: String!
  ) {
    getHospitalHmoPayments(
      filterOptions: $filterOptions
      hospitalId: $hospitalId
      providerId: $providerId
    ) {
      list {
        QAOn
        adjudicatedOn
        batchDate
        batchNumber
        batchPaidAmount
        batchRefundDate
        batchStatusId
        batchTotal
        batchedBy
        encounterMonth
        maximumDate
        minimumDate
        provider
        providerBatchStatus
        receiveDateReception
        reference
        statusDescription
        units
      }
    }
  }
`;

export const GENERATE_HOSPITAL_HMO_PAYMENTS_DOCS = gql`
  mutation GenerateHospitalHmoPayments($providerId: String!) {
    generateHospitalPaymentDocuments(providerId: $providerId) {
      success
      errorMsg
      documentUrl
      excelUrl
    }
  }
`;

export const GET_CLAIMS_SUMMARY = gql`
  query GetClaimsSummary($filterOptions: HmoClaimFilterInput!) {
    getClaimsSummary(filterOptions: $filterOptions) {
      totalApprovedClaims
      totalRejectedClaims
      totalClaims
      totalClaimsAmount
      totalApprovedClaimsAmount
      totalRejectedClaimsAmount
      totalPaidClaims
      totalPaidClaimsAmount
    }
  }
`;

export const GET_HMO_PORTAL_REGISTERED_ACCOUNTS = gql`
  query GetHmoPortalRegisteredAccounts {
    getProviderStaffs {
      providerName
      staffs {
        email
        name
      }
    }
  }
`;

export const PROCESS_CLAIM_STATUS = gql`
  mutation ProcessClaimStatus($claimId: String!, $status: String!) {
    updateClaimStatus(status: $status, claimId: $claimId) {
      id
      status
      revertAction {
        action
        mutatorAlias
        receiverAlias
        mutatorUserType
        receiverUserType
      }
      updatedDate
      lastModifierName
    }
  }
`;

export const BULK_UPDATE_CLAIM_STATUS = gql`
  mutation BulkUpdateClaimStatus($claimIds: [String!]!, $status: String!) {
    bulkUpdateClaimStatus(status: $status, claimIds: $claimIds) {
      id
      status
      updatedDate
      lastModifierName
    }
  }
`;

export const BULK_UPDATE_CLAIMS_UTILIZATION_STATUS = gql`
  mutation BulkUpdateClaimsUtilizationStatus($input: UpdateUtilizationsStatusInput!) {
    updateClaimUtilizationsStatus(input: $input) {
      id
      status
      lastModifierName
      updatedDate
    }
  }
`;

export const FLAG_CLAIM = gql`
  mutation FlagClaim($id: String!, $flag: String!, $unset: Boolean!, $unsetAllExisting: Boolean) {
    flagClaim(id: $id, flag: $flag, unset: $unset, unsetAllExisting: $unsetAllExisting) {
      id
      flags {
        flag
      }
      lastModifierId
      lastModifierName
    }
  }
`;

export const HMO_CLAIM_UPDATED_SUBS = gql`
  subscription HMOClaimUpdatedSubs(
    $profileId: String!
    $hospitalId: String!
    $hmoProviderId: String
  ) {
    HMOClaimUpdated(profileId: $profileId, hospitalId: $hospitalId, hmoProviderId: $hmoProviderId) {
      ...HmoClaim
      financeApproval {
        createdDate
        creatorId
        creatorName
        approvalGroup
      }
    }
  }
  ${HMO_CLAIM}
`;

export const HMO_CLAIM_ADDED_SUBS = gql`
  subscription HMOClaimAddedSubs(
    $profileId: String!
    $hospitalId: String!
    $hmoProviderId: String
  ) {
    HMOClaimAdded(profileId: $profileId, hospitalId: $hospitalId, hmoProviderId: $hmoProviderId) {
      ...HmoClaim
      hospitalId
      createdDate
      responseDateTime
      utilizations {
        ...PreauthUtilisations
        paUtilProcessed
      }
    }
  }
  ${HMO_CLAIM}
  ${UTILIZATION}
`;

export const UPDATE_UTILIZATION_QUANTITY = gql`
  mutation UpdateUtilizationQuantity($id: String!, $quantity: Float!) {
    updateUtilizationQuantity(id: $id, quantity: $quantity) {
      id
      quantity
      lastModifierId
      lastModifierName
    }
  }
`;

export const HMO_CLAIM_FLAGGED_SUBS = gql`
  subscription HmoClaimFlagged($hospitalId: String!) {
    HmoClaimFlagged(hospitalId: $hospitalId) {
      id
      flags {
        flag
      }
      lastModifierId
      lastModifierName
      hospitalId
    }
  }
`;

export const GET_PROVIDERS_WITH_SUBMITTED_CLAIMS = gql`
  query GetProvidersWithSubmittedClaims($filterOptions: HmoClaimFilterInput!) {
    getProvidersWithSubmittedClaims(filterOptions: $filterOptions) {
      id
      name
      address
      hospitalSupportPhoneNumber {
        countryName
        countryCode
        value
      }
      supportMail
    }
  }
`;

export const SEND_PAYMENT_ADVICE_TO_PROVIDERS = gql`
  mutation SendPaymentAdviceToProviders(
    $filterOptions: HmoClaimFilterInput!
    $hmoClaimIds: [String!]
    $origin: String!
  ) {
    sendHmoClaimsPaymentAdviceToProvider(
      filterOptions: $filterOptions
      hmoClaimIds: $hmoClaimIds
      origin: $origin
    )
  }
`;

export const UPDATE_CLAIM_UTILIZATION_CONFIRMATION = gql`
  mutation UpdateClaimUtilizationConfirmation(
    $utilizationIds: [String!]!
    $confirmation: Boolean!
    $claimId: String!
  ) {
    confirmUtilization(
      claimId: $claimId
      confirmation: $confirmation
      utilizationIds: $utilizationIds
    ) {
      id
      confirmation
      hmoClaimId
    }
  }
`;

export const CONFIRM_UTILIZATIONS_SUBS = gql`
  subscription UtilizationsConfirmed($hospitalId: String!) {
    UtilizationsConfirmed(hospitalId: $hospitalId) {
      id
      confirmation
      hmoClaimId
    }
  }
`;

export const UPDATE_CLAIM_UTILIZATION_COVERED = gql`
  mutation UpdateClaimUtilizationCovered(
    $claimId: String!
    $utilizationId: String!
    $percentageCovered: Float
    $amountCovered: Float
  ) {
    updateClaimUtilizationCovered(
      utilizationId: $utilizationId
      percentageCovered: $percentageCovered
      amountCovered: $amountCovered
      claimId: $claimId
    ) {
      id
      percentageCovered
      amountCovered
    }
  }
`;

export const SEND_ENROLLEE_HMO_CLAIM = gql`
  mutation SendEnrolleeHmoClaim($hmoClaimIds: [String!]!, $origin: String!) {
    sendEnrolleeHmoClaimEmail(hmoClaimIds: $hmoClaimIds, origin: $origin)
  }
`;

export const HMO_CLAIM_CONFIRMATION_SUBS = gql`
  subscription HmoClaimConfirmationSubs($hmoProviderId: String!) {
    HmoClaimConfirmation(hmoProviderId: $hmoProviderId) {
      id
      confirmation
    }
  }
`;

export const CLAIM_UTILIZATION_COVERED_UPDATED_SUBS = gql`
  subscription UtilizationCoveredUpdatedSubs($hospitalId: String!) {
    UtilizationCoveredUpdated(hospitalId: $hospitalId) {
      id
      percentageCovered
      amountCovered
    }
  }
`;

export const PAYOUT_CLAIMS = gql`
  mutation PayoutClaims($ids: [String!]!, $hospitalId: String!, $origin: String!) {
    payoutClaims(ids: $ids, hospitalId: $hospitalId, origin: $origin) {
      ...HmoClaim
    }
  }
  ${HMO_CLAIM}
`;

export const CLAIM_ACCOUNT_HMO_CLAIM_APPROVAL = gql`
  mutation ClaimAccountHmoClaimApproval($ids: [String!]!, $status: Boolean!) {
    claimFinanceApproval(claimIds: $ids, status: $status) {
      id
      financeApproval {
        createdDate
        creatorId
        creatorName
        approvalGroup
      }
    }
  }
`;
