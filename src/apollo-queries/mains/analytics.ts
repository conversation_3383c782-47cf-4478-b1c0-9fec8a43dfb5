import { gql } from '@apollo/client';

// Finance Analytics
export const FINANCE_DATA = gql`
  fragment FinanceData on FinanceData {
    name
    totalTransactions
    totalDiscount
    totalRevenue
    totalAmount
    totalAmountPaid
    totalPending
    totalPaid
    totalPartial
    totalCancelled
    totalAmountOutstanding
    raisedBy
    category
    coverageName
    coverageType
    enrolleeId
    patientClinifyId
    patientEmailAddress
    patientName
    patientPhoneNumber
    quantity
    serviceName
    serviceType
    visitationDate
    totalAmountDue
    totalAmountDeposited
    totalAmountAvailable
    totalAmountWithdrawn
    totalAmountRefunded
    billSummary {
      hospitalName
      hospitalId
      totalAmountOutstanding
      totalAmountDue
      totalDiscount
      totalAmount
      totalAmountPaid
      totalRevenue
      totalPaid
      totalPending
      totalCancelled
      totalPartial
      totalTransactions
    }
    depositSummary {
      hospitalName
      hospitalId
      totalAmountDeposited
      totalAmountRefunded
      totalAmountWithdrawn
      totalAmountAvailable
    }
  }
`;

export const GET_FINANCE_SUMMARY = gql`
  query GetFinanceSummary($filter: FinanceAnalyticsFilter) {
    getFinanceSummary(filter: $filter) {
      ...FinanceData
    }
  }
  ${FINANCE_DATA}
`;

export const GET_FINANCE_DATA = gql`
  query GetFinanceData($filter: FinanceAnalyticsFilter) {
    getFinanceData(filter: $filter) {
      ...FinanceData
    }
  }
  ${FINANCE_DATA}
`;

export const GET_BILL_STATUS_DATA = gql`
  query GetBillStatusData($filter: FinanceAnalyticsFilter) {
    getBillStatusData(filter: $filter) {
      ...FinanceData
    }
  }
  ${FINANCE_DATA}
`;

export const GET_HMO_REVENUE = gql`
  query GetHMORevenueData($filter: FinanceAnalyticsFilter) {
    getHMORevenueData(filter: $filter) {
      data {
        name
        category
        serviceName
        totalAmount
        totalAmountPaid
        totalAmountOutstanding
        totalAmountDue
        patientFullName
        enrolleeId
        visitDate
        providerId
        patientPhoneNumber
        patientEmailAddress
        billId
        quantity
        raisedBy
        splitPaymentTypes
        splitAmountPaid
        splitBankNames
        splitAccountNumbers
      }
    }
  }
`;

export const GET_COMPANY_REVENUE = gql`
  query GetCompanyRevenueData($filter: FinanceAnalyticsFilter) {
    getCompanyRevenueData(filter: $filter) {
      data {
        name
        category
        serviceName
        totalAmount
        totalAmountPaid
        totalAmountOutstanding
        totalAmountDue
        patientFullName
        enrolleeId
        visitDate
        coverageInformationId
        companyName
        patientPhoneNumber
        patientEmailAddress
        billId
        quantity
        raisedBy
        bankName
        accountNumber
        splitPaymentTypes
        splitAmountPaid
        splitBankNames
        splitAccountNumbers
      }
    }
  }
`;
export const GET_PRIVATE_REVENUE = gql`
  query GetPrivateRevenueData($filter: FinanceAnalyticsFilter) {
    getPrivateRevenueData(filter: $filter) {
      data {
        name
        category
        totalAmount
        totalAmountOutstanding
        totalAmountPaid
        totalAmountDue
        patientFullName
        visitDate
        serviceName
        patientPhoneNumber
        patientEmailAddress
        billId
        quantity
        raisedBy
        bankName
        accountNumber
        splitPaymentTypes
        splitAmountPaid
        splitBankNames
        splitAccountNumbers
      }
    }
  }
`;

export const GET_FAMILY_REVENUE = gql`
  query GetFamilyRevenueData($filter: FinanceAnalyticsFilter) {
    getFamilyRevenueData(filter: $filter) {
      data {
        name
        category
        totalAmount
        totalAmountOutstanding
        totalAmountPaid
        totalAmountDue
        patientFullName
        visitDate
        serviceName
        coverageInformationId
        familyName
        patientPhoneNumber
        patientEmailAddress
        billId
        quantity
        raisedBy
        bankName
        accountNumber
        splitPaymentTypes
        splitAmountPaid
        splitBankNames
        splitAccountNumbers
      }
    }
  }
`;

export const GET_REVENUE_BY_DEPARTMENT = gql`
  query GetRevenueByDepartment($filter: FinanceAnalyticsFilter) {
    getRevenueByDepartment(filter: $filter) {
      name
      category
      totalAmountDue
      totalAmountPaid
      totalAmountOutstanding
      totalDiscount
      totalAmount
      totalRevenue
      visitationDate
      serviceType
      serviceName
      quantity
      raisedBy
      patientName
      patientClinifyId
      patientPhoneNumber
      patientEmailAddress
      coverageType
      coverageName
      enrolleeId
    }
  }
`;

export const GET_REVENUE_BY_SPECIALTY = gql`
  query GetRevenueBySpecialty($filter: FinanceAnalyticsFilter) {
    getRevenueBySpecialty(filter: $filter) {
      name
      category
      totalAmountPaid
      totalAmountOutstanding
      totalDiscount
      totalRevenue
      totalAmount
      totalAmountDue
      visitationDate
      serviceType
      serviceName
      quantity
      raisedBy
      patientName
      patientClinifyId
      patientPhoneNumber
      patientEmailAddress
      coverageType
      coverageName
      enrolleeId
    }
  }
`;

export const GET_SERVICE_REVENUE = gql`
  query GetServiceRevenueData($filter: FinanceAnalyticsFilter) {
    getServiceRevenueData(filter: $filter) {
      cardSummary {
        service_type
        count
      }
      data {
        name
        category
        totalAmount
        totalAmountDue
        totalAmountOutstanding
        totalDiscount
        totalAmountPaid
        totalRevenue
        visitationDate
        serviceType
        serviceName
        quantity
        raisedBy
        patientName
        patientClinifyId
        patientPhoneNumber
        patientEmailAddress
        coverageType
        coverageName
        enrolleeId
      }
    }
  }
`;

export const GET_PAYMENT_TYPE_DATA = gql`
  query GetPaymentTypeData($filter: FinanceAnalyticsFilter) {
    getPaymentTypeData(filter: $filter) {
      cardSummary {
        paymentType
        count
      }
      data {
        name
        category
        totalAmount
        totalAmountDue
        totalAmountOutstanding
        totalDiscount
        totalAmountPaid
        totalRevenue
        visitationDate
        serviceType
        serviceName
        quantity
        raisedBy
        patientName
        patientClinifyId
        patientPhoneNumber
        patientEmailAddress
        coverageType
        coverageName
        enrolleeId
      }
    }
  }
`;

// Visitation Analytics
export const VISITATION_SUMMARY = gql`
  fragment VisitationSummary on VisitationSummary {
    name
    totalRegisteredPatients
    totalRegisteredPatientsUnderOneYear
    totalAppointmentsBooked
    totalAppointmentsCompleted
    totalAppointmentsMissed
    totalAppointmentsCancelled
    totalVisitations
    totalEmergency
    totalNonEmergency
    totalInPatient
    totalOutPatient
    totalAdmissions
    totalReferralOut
    totalReferralIn
    totalWaitTimeInMinutes
    groupedData {
      totalVisitations {
        hospitalName
        hospitalId
        count
      }
      totalAppointmentsBooked {
        hospitalName
        hospitalId
        count
      }
      totalAppointmentsCancelled {
        hospitalName
        hospitalId
        count
      }
      totalAppointmentsCompleted {
        hospitalName
        hospitalId
        count
      }
      totalAppointmentsMissed {
        hospitalName
        hospitalId
        count
      }
      totalRegisteredPatients {
        hospitalName
        hospitalId
        count
      }
      totalRegisteredPatientsUnderOneYear {
        hospitalName
        hospitalId
        count
      }
      totalEmergency {
        hospitalName
        hospitalId
        count
      }
      totalNonEmergency {
        hospitalName
        hospitalId
        count
      }
      totalInPatient {
        hospitalName
        hospitalId
        count
      }
      totalOutPatient {
        hospitalName
        hospitalId
        count
      }
      totalReferral {
        hospitalName
        hospitalId
        count
        totalReferralIn
        totalReferralOut
      }
      totalWaitTimeInMinutes {
        hospitalName
        hospitalId
        count
      }
    }
  }
`;

export const GET_VISITATION_SUMMARY = gql`
  query GetVisitationSummary($filter: VisitationAnalyticsFilter) {
    getVisitationSummary(filter: $filter) {
      ...VisitationSummary
    }
  }
  ${VISITATION_SUMMARY}
`;

export const GET_VISITATION_DATA = gql`
  query GetVisitationData($filter: VisitationAnalyticsFilter) {
    getVisitationData(filter: $filter) {
      ...VisitationSummary
      name
    }
  }
  ${VISITATION_SUMMARY}
`;

export const GET_REGISTERED_PATIENTS_DATA = gql`
  query GetRegisteredPatientsData($filter: VisitationAnalyticsFilter) {
    getRegisteredPatientsData(filter: $filter) {
      name
      totalFemale
      totalMale
      ageRanges {
        count
        category
      }
      categoryByGenders {
        count
        category
        group
      }
    }
  }
`;

export const GET_PATIENTS_TYPE_DATA = gql`
  query GetPatientsType($filter: VisitationAnalyticsFilter) {
    getPatientsType(filter: $filter) {
      totalInPatient
      totalOutPatient
      name
    }
  }
`;

export const GET_APPOINTMENT_STATUS_DATA = gql`
  query GetAppointmentStatusData($filter: VisitationAnalyticsFilter) {
    getAppointmentStatusData(filter: $filter) {
      totalAppointmentsCompleted
      totalAppointmentsMissed
      totalAppointmentsCancelled
      name
    }
  }
`;

export const GET_VISITATION_BY_DEPARTMENT_DATA = gql`
  query GetVisitationsByDepartment($filter: VisitationAnalyticsFilter) {
    getVisitationsByDepartment(filter: $filter) {
      department
      count
      name
      patientFullName
    }
  }
`;
export const GET_VISITATION_BY_GENDER = gql`
  query GetVisitationsByGender($filter: VisitationAnalyticsFilter) {
    getVisitationsByGender(filter: $filter) {
      name
      totalFemale
      totalMale
      ageRanges {
        count
        category
      }
      categoryByGenders {
        count
        category
        group
      }
    }
  }
`;

export const GET_VISITATION_BY_PATIENT_COVERAGE = gql`
  query GetVisitationsByPatientCoverage($filter: VisitationAnalyticsFilter) {
    getVisitationsByPatientCoverage(filter: $filter) {
      name
      byCoverageType {
        hmo {
          coverageName
          memberNumber
          patientFullName
          visitDate
        }
        private {
          coverageName
          memberNumber
          patientFullName
          visitDate
        }
        company {
          coverageName
          memberNumber
          patientFullName
          visitDate
        }
        family {
          coverageName
          memberNumber
          patientFullName
          visitDate
        }
      }
    }
  }
`;
export const GET_AVERAGE_WAIT_TIME_BY_DEPARTMENT_DATA = gql`
  query GetAverageWaitTimeByDepartment($filter: VisitationAnalyticsFilter) {
    getAverageWaitTimeByDepartment(filter: $filter) {
      name
      category
      totalWaitTime
    }
  }
`;

// Admission Analytics
export const ADMISSION_SUMMARY = gql`
  fragment AdmissionSummary on AdmissionSummary {
    totalAdmissions
    totalDischarge
    averageLengthOfStay
    totalTransfers
    totalBeds
    totalBedOccupied
    totalBedAvailable
    groupedData {
      admissions {
        hospitalId
        hospitalName
        totalAdmissions
        averageLengthOfStay
      }
      beds {
        hospitalId
        hospitalName
        totalBedAvailable
        totalBedOccupied
        totalBeds
      }
      totalDischarge {
        hospitalId
        hospitalName
        count
      }
      totalTransfers {
        hospitalId
        hospitalName
        count
      }
    }
  }
`;

export const GET_ADMISSION_SUMMARY = gql`
  query GetAdmissionSummary($filter: AdmissionAnalyticsFilter) {
    getAdmissionSummary(filter: $filter) {
      ...AdmissionSummary
    }
  }
  ${ADMISSION_SUMMARY}
`;

export const GET_ADMISSION_DATA = gql`
  query GetAdmissionData($filter: AdmissionAnalyticsFilter) {
    getAdmissionData(filter: $filter) {
      ...AdmissionSummary
      name
    }
  }
  ${ADMISSION_SUMMARY}
`;

export const GET_TRANSFER_DATA = gql`
  query GetTransferPatientAnalytics($filter: AdmissionAnalyticsFilter) {
    getTransferPatient(filter: $filter) {
      totalTransfers
      name
    }
  }
`;

export const GET_ADMISSION_BY_DEPARTMENT_DATA = gql`
  query GetAdmissionByDepartment($filter: AdmissionAnalyticsFilter) {
    getAdmissionByDepartment(filter: $filter) {
      category
      count
      name
    }
  }
`;

export const GET_TRANSFER_BY_FACILITY_DATA = gql`
  query GetTransferByFacilityData($filter: AdmissionAnalyticsFilter) {
    getTransferByFacilityData(filter: $filter) {
      category
      count
      name
    }
  }
`;

export const GET_DISCHARGE_BY_DEPARTMENT_DATA = gql`
  query GetDischargeByDepartment($filter: AdmissionAnalyticsFilter) {
    getDischargeByDepartment(filter: $filter) {
      category
      count
      name
    }
  }
`;

export const GET_ADMISSION_DISCHARGE_DATA = gql`
  query GetAdmissionDischargeData($filter: AdmissionAnalyticsFilter) {
    getAdmissionDischargeData(filter: $filter) {
      summary {
        totalAdmissions
        totalDischarge
        name
      }
      list {
        patientName
        patientAge
        patientGender
        diagnosis
        dateOfAdmission
        dateOfDischarge
        noOfDaysSpent
      }
    }
  }
`;

export const GET_LENGTH_OF_STAY_BY_DEPARTMENT_DATA = gql`
  query GetLengthOfStayByDepartment($filter: AdmissionAnalyticsFilter) {
    getLengthOfStayByDepartment(filter: $filter) {
      category
      count
      name
      averageLengthOfStay
    }
  }
`;

export const GET_BED_AVAILABILITY_DATA = gql`
  query GetBedAvailabilityData($filter: AdmissionAnalyticsFilter) {
    getBedAvailabilityData(filter: $filter) {
      totalBedOccupied
      totalBedAvailable
      name
    }
  }
`;

// Facility Staffs Analytics
export const FACILITY_STAFFS_SUMMARY = gql`
  fragment FacilityStaffsData on FacilityStaffsData {
    name
    totalStaffs
    totalMaleStaffs
    totalFemaleStaffs
    totalActiveStaffs
    totalInActiveStaffs
    totalRoles
    staffSummary {
      hospitalName
      hospitalId
      totalActiveStaffs
      totalFemaleStaffs
      totalInActiveStaffs
      totalMaleStaffs
      totalStaffs
    }
    rolesSummary {
      hospitalName
      count
    }
  }
`;

export const GET_FACILITY_STAFFS_SUMMARY = gql`
  query GetFacilityStaffsSummary($filter: FacilityStaffsAnalyticsFilter) {
    getFacilityStaffsSummary(filter: $filter) {
      ...FacilityStaffsData
    }
  }
  ${FACILITY_STAFFS_SUMMARY}
`;

export const GET_FACILITY_STAFFS_DATA = gql`
  query GetFacilityStaffsData($filter: FacilityStaffsAnalyticsFilter) {
    getFacilityStaffsData(filter: $filter) {
      ...FacilityStaffsData
    }
  }
  ${FACILITY_STAFFS_SUMMARY}
`;

export const GET_STAFFS_GENDER_DATA = gql`
  query GetFacilityStaffsGenderData($filter: FacilityStaffsAnalyticsFilter) {
    getGenderByDepartment(filter: $filter) {
      totalMaleStaffs
      totalFemaleStaffs
      name
      category
    }
  }
`;

export const GET_ACCOUNT_STATUS_DATA = gql`
  query GetAccountStatusData($filter: FacilityStaffsAnalyticsFilter) {
    getAccountStatusByDepartment(filter: $filter) {
      name
      category
      totalActiveStaffs
      totalInActiveStaffs
    }
  }
`;

export const GET_STAFF_COUNT_DATA = gql`
  query GetStaffCountData($filter: FacilityStaffsAnalyticsFilter) {
    getStaffsByDepartment(filter: $filter) {
      name
      category
      count
    }
  }
`;

// Services Analytics
export const SERVICES_SUMMARY = gql`
  fragment ServicesSummary on ServicesSummary {
    name
    totalAntenatals
    totalAntenatalsAmount
    totalAdmissions
    totalAdmissionsAmount
    totalMedications
    totalProcedures
    totalProceduresAmount
    totalConsultations
    totalConsultationsAmount
    totalMedicationsDispensed
    totalMedicationsDispensedAmount
    totalConsumablesDispensed
    totalRequestedLaboratoryAmount
    totalConsumablesDispensedAmount
    totalMedicationsPrescribed
    totalMedicationsPrescribedAmount
    totalConsumablesPrescribed
    totalConsumablesPrescribedAmount
    totalImmunizations
    totalImmunizationsAmount
    totalInvestigations
    totalRadiology
    totalRadiologyAmount
    totalLaboratory
    totalLaboratoryAmount
    totalRequestedLaboratory
    totalRequestedRadiologyAmount
    totalProcessedLaboratory
    totalProcessedLaboratoryAmount
    totalProcessedRadiology
    totalProcessedRadiologyAmount
    totalRequestedRadiology
    totalRequestedRadiologyAmount
    totalNursingServices
    totalNursingServicesAmount
    totalMedicalReports
    totalMedicalReportsAmount
    antenatalsSummary {
      totalAmount
      hospitalName
      count
    }
    admissionsSummary {
      totalAmount
      hospitalName
      count
    }
    consultationsSummary {
      totalAmount
      hospitalName
      count
    }
    proceduresSummary {
      totalAmount
      hospitalName
      count
    }
    immunizationsSummary {
      totalAmount
      hospitalName
      count
    }
    medicationsDispensedSummary {
      totalAmount
      hospitalName
      count
    }
    consumablesDispensedSummary {
      totalAmount
      hospitalName
      count
    }
    nursingServicesSummary {
      totalAmount
      hospitalName
      count
    }
    medicalReportsSummary {
      totalAmount
      hospitalName
      count
    }
    radiologySummary {
      totalAmount
      hospitalName
      count
    }
    laboratorySummary {
      totalAmount
      hospitalName
      count
    }
    medicationsPrescribedSummary {
      totalAmount
      hospitalName
      count
    }
    consumablesPrescribedSummary {
      totalAmount
      hospitalName
      count
    }
    requestedLaboratorySummary {
      totalAmount
      hospitalName
      count
    }
    requestedRadiologySummary {
      totalAmount
      hospitalName
      count
    }
    processedLaboratorySummary {
      totalAmount
      hospitalName
      count
    }
    processedRadiologySummary {
      totalAmount
      hospitalName
      count
    }
  }
`;

export const GET_SERVICES_SUMMARY = gql`
  query GetServicesSummary($filter: ServicesAnalyticsFilter) {
    getServicesSummary(filter: $filter) {
      ...ServicesSummary
    }
  }
  ${SERVICES_SUMMARY}
`;

export const GET_SERVICES_DATA = gql`
  query GetServicesData($filter: ServicesAnalyticsFilter) {
    getServicesData(filter: $filter) {
      ...ServicesSummary
      name
    }
  }
  ${SERVICES_SUMMARY}
`;

export const GET_CONSULTATION_DATA = gql`
  query GetConsultationData($filter: ServicesAnalyticsFilter) {
    getConsultationData(filter: $filter) {
      averageNumber
      data {
        name
        totalConsultations
      }
      provisionalDiagnosis {
        diagnosisICD10 {
          male
          female
        }
        diagnosisICD11 {
          male
          female
        }
        diagnosisSNOMED {
          male
          female
        }
      }
      finalDiagnosis {
        diagnosisICD10 {
          male
          female
        }
        diagnosisICD11 {
          male
          female
        }
        diagnosisSNOMED {
          male
          female
        }
      }
      byChemoDiagnosis {
        chemoDiagnosis
        male
        female
      }
      byConsultationDateAndDoctorName {
        consultationDate
        doctorName
        finalDiagnosis
        provisionalDiagnosis
        patientName
        clinifyId
        paymentType
        priority
      }
      byChemoDiagnosisAndDoctorName {
        consultationDate
        doctorName
        chemoDiagnosis
      }
    }
  }
`;

export const GET_PROCEDURE_DATA = gql`
  query GetProcedureData($filter: ServicesAnalyticsFilter) {
    getProcedureData(filter: $filter) {
      averageNumber
      data {
        name
        totalProcedures
      }
      byPatientName {
        name
        operationDate
        operatedBy
        patientName
        clinifyId
        paymentType
        totalAmount
        quantity
        gender
        priority
      }
    }
  }
`;

export const GET_RADIOLOGY_INVESTIGATION_DATA = gql`
  query GetRadiologyInvestigationData($filter: ServicesAnalyticsFilter) {
    getRadiologyInvestigationData(filter: $filter) {
      servicesSummary {
        totalRadiology
        name
      }
      list {
        examTypes {
          male {
            name
            count
            totalAmount
            quantity
          }
          female {
            name
            count
            totalAmount
            quantity
          }
        }
        byExaminationDate {
          examinationDate
          investigationNames
          radiographerName
        }
        byPatientName {
          patientName
          examinationDate
          name
          radiographerName
          totalAmount
          clinifyId
          quantity
          paymentType
        }
      }
    }
  }
`;

export const GET_LABORATORY_INVESTIGATION_DATA = gql`
  query GetLaboratoryInvestigationData($filter: ServicesAnalyticsFilter) {
    getLaboratoryInvestigationData(filter: $filter) {
      servicesSummary {
        totalLaboratory
        name
      }
      list {
        testInfos {
          male {
            name
            count
            quantity
            totalAmount
          }
          female {
            name
            count
            quantity
            totalAmount
          }
        }
        byLabTestDate {
          testDate
          testVerifiedBy
          investigationNames
        }
        byPatientName {
          patientName
          testDate
          paymentType
          performedBy
          totalAmount
          clinifyId
          quantity
          name
        }
      }
    }
  }
`;

export const GET_IMMUNIZATION_DATA = gql`
  query GetImmunizationData($filter: ServicesAnalyticsFilter) {
    getImmunizationData(filter: $filter) {
      servicesSummary {
        totalImmunizations
        name
      }
      list {
        immunizationNames {
          male {
            name
            quantity
            count
            totalAmount
          }
          female {
            name
            quantity
            count
            totalAmount
          }
        }
        byImmunizationAdmistrationDateAndAdmistratorName {
          admistratorName
          administrationDate
          routeOfAdministration
          immunizationNames
          totalAmount
        }
        byPatientName {
          name
          administrationDate
          admistratorName
          patientName
          paymentType
          totalAmount
          quantity
          clinifyId
          priority
        }
      }
    }
  }
`;

export const GET_MEDICATION_PRESCRIBED_DATA = gql`
  query GetMedicationPrescibedData($filter: ServicesAnalyticsFilter) {
    getMedicationPrescibedData(filter: $filter) {
      servicesSummary {
        totalMedicationsPrescribed
        name
      }
      list {
        medicationNames {
          male {
            name
            quantity
          }
          female {
            name
            quantity
          }
        }
        byPatientName {
          patientName
          quantity
          name
          prescribedDate
          prescribedBy
          dosage
          dosageUnit
          medicationCategory
        }
        byMedicationPrescriptionDateAndPrescriberName {
          prescriptionDate
          prescribedBy
          medicationNames
          quantity
        }
        byMost {
          name
          total
        }
        byLeast {
          name
          total
        }
      }
    }
  }
`;

export const GET_CONSUMABLE_PRESCRIBED_DATA = gql`
  query GetConsumablePrescribedData($filter: ServicesAnalyticsFilter) {
    getConsumablePrescribedData(filter: $filter) {
      servicesSummary {
        totalConsumablesPrescribed
        name
      }
      list {
        consumables {
          male {
            name
            quantity
          }
          female {
            name
            quantity
          }
        }
        byConsumablePrescriptionDateAndPrescriberName {
          prescriptionDate
          prescribedBy
          medicationNames
          quantity
        }
        byMost {
          name
          total
        }
        byLeast {
          name
          total
        }

        byPatientName {
          patientName
          quantity
          name
          prescribedDate
          prescribedBy
          clinifyId
        }
      }
    }
  }
`;

export const GET_MEDICATION_DISPENSED_DATA = gql`
  query GetMedicationDispenseData($filter: ServicesAnalyticsFilter) {
    getMedicationDispenseData(filter: $filter) {
      servicesSummary {
        totalMedicationsDispensed
        name
      }
      list {
        medicationNames {
          male {
            name
            quantity
            totalAmount
          }
          female {
            name
            quantity
            totalAmount
          }
        }
        byPatientName {
          patientName
          totalAmount
          quantity
          name
          dispenseDate
          dispensedBy
          dosage
          dosageUnit
          medicationCategory
          paymentType
          gender
          patientAge
          quantityRemaining
        }
        byMedicationDispensedDateAndDispenserName {
          dispenseDate
          dispensedBy
          quantity
          medicationNames
        }
        byMost {
          name
          total
        }
        byLeast {
          name
          total
        }
      }
    }
  }
`;

export const GET_CONSUMABLE_DISPENSED_DATA = gql`
  query GetConsumableDispenseData($filter: ServicesAnalyticsFilter) {
    getConsumableDispenseData(filter: $filter) {
      servicesSummary {
        totalAmount
        totalConsumablesDispensed
        name
      }
      list {
        consumables {
          male {
            name
            quantity
            totalAmount
          }
          female {
            name
            quantity
            totalAmount
          }
        }
        byConsumableDispensedDateAndDispenserName {
          dispenseDate
          dispensedBy
          quantity
          medicationNames
        }
        byMost {
          name
          total
        }
        byLeast {
          name
          total
        }
        byPatientName {
          patientName
          quantity
          name
          dispenseDate
          dispensedBy
          clinifyId
          paymentType
          totalAmount
        }
      }
    }
  }
`;

export const GET_SERVICES_ADMISSION_DATA = gql`
  query GetServiceAdmissionData($filter: ServicesAnalyticsFilter) {
    getServiceAdmissionData(filter: $filter) {
      servicesSummary {
        totalAdmissions
        name
      }
      list {
        admissionDiagnosis {
          diagnosisICD10 {
            male
            female
          }
          diagnosisICD11 {
            male
            female
          }
          diagnosisSNOMED {
            male
            female
          }
          byAdmittedDateAndAdmittedBy {
            admittedBy
            admissionDate
            diagnosis
            clinifyId
            patientName
            paymentType
            status
            priority
            totalLengthOfStay
          }
        }
      }
    }
  }
`;

export const GET_BIRTH_DATA = gql`
  query GetBirthData($filter: ServicesAnalyticsFilter) {
    getBirthData(filter: $filter) {
      summary {
        totalMale
        totalFemale
      }
      data {
        name
        totalMale
        totalFemale
      }
    }
  }
`;

export const GET_DEMOGRAPHIC_DATA = gql`
  query GetDemographicData($filter: ServicesAnalyticsFilter) {
    getDemographicData(filter: $filter) {
      category
      name
      totalMale
      totalFemale
    }
  }
`;

export const GET_INVESTIGATION_STATUS_DATA = gql`
  query GetInvestigationStatusData($filter: ServicesAnalyticsFilter) {
    getInvestigationStatusData(filter: $filter) {
      name
      totalRequestedRadiology
      totalProcessedRadiology
      totalProcessedLaboratory
      totalRequestedLaboratory
      totalRequestedRadiologyAmount
      totalProcessedRadiologyAmount
      totalRequestedLaboratoryAmount
      totalProcessedLaboratoryAmount

      listLabTestByPatient {
        patientName
        testDate
        paymentType
        performedBy
        totalAmount
        clinifyId
        quantity
        name
      }
      listRadiologyByPatient {
        patientName
        examinationDate
        name
        radiographerName
        totalAmount
        clinifyId
        quantity
        paymentType
      }
    }
  }
`;

export const GET_TOP_SERVICES_DATA = gql`
  query TopServicesData($filter: ServicesAnalyticsFilter) {
    getTopServicesData(filter: $filter) {
      procedureResult {
        count
        category
        totalAmount
        totalQuantity
        totalAmountDue
        totalAmountPaid
        totalAmountOutstanding
      }
      immunizationResult {
        count
        category
        totalAmount
        totalQuantity
        totalAmountDue
        totalAmountPaid
        totalAmountOutstanding
      }
      medicationResult {
        count
        category
      }
      investigationLabResult {
        count
        category
        totalAmount
        totalQuantity
        totalAmountDue
        totalAmountPaid
        totalAmountOutstanding
      }
      investigationRadResult {
        count
        category
        totalAmount
        totalQuantity
        totalAmountDue
        totalAmountPaid
        totalAmountOutstanding
      }
      admissionResult {
        count
        category
        totalAmount
        totalQuantity
        totalAmountDue
        totalAmountPaid
        totalAmountOutstanding
      }
      consultationResult {
        count
        category
        totalAmount
        totalQuantity
        totalAmountDue
        totalAmountPaid
        totalAmountOutstanding
      }
      medicationDispenseResult {
        count
        category
        totalAmount
        totalQuantity
        totalAmountDue
        totalAmountPaid
        totalAmountOutstanding
      }
      medicationPrescribedResult {
        count
        category
      }
      consumableDispensedResult {
        count
        category
        totalAmount
        totalQuantity
        totalAmountDue
        totalAmountPaid
        totalAmountOutstanding
      }
      consumablePrescribedResult {
        count
        category
      }
      investigationResult {
        totalRequestedLaboratory
        totalProcessedLaboratory
        totalProcessedRadiology
        totalRequestedRadiology
      }
      nursingServiceResult {
        count
        category
        totalAmount
        totalQuantity
        totalAmountDue
        totalAmountPaid
        totalAmountOutstanding
      }
      medicalReportResult {
        count
        category
        totalAmount
        totalQuantity
        totalAmountDue
        totalAmountPaid
        totalAmountOutstanding
      }
    }
  }
`;

// Stocks Analytics
export const STOCKS_SUMMARY = gql`
  fragment StocksSummary on StocksSummary {
    totalDevices
    totalConsumables
    totalRooms
    totalDrugs
    totalVehicles
    totalSuppliers
    totalLabs
    summary {
      totalDevices
      totalConsumables
      totalRooms
      totalDrugs
      totalVehicles
      totalSuppliers
      totalLabs
      hospitalName
    }
  }
`;

export const GET_STOCKS_SUMMARY = gql`
  query GetStocksSummary($filter: StocksAnalyticsFilter) {
    getStocksSummary(filter: $filter) {
      ...StocksSummary
    }
  }
  ${STOCKS_SUMMARY}
`;

export const GET_ITEM_SALES_DATA = gql`
  query GetItemSalesData($filter: StocksAnalyticsFilter) {
    getItemSalesData(filter: $filter) {
      category
      name
      totalCosts
      totalSales
      totalRevenue
    }
  }
`;

export const GET_LAB_STATUS_DATA = gql`
  query GetLabStatusData($filter: StocksAnalyticsFilter!) {
    getLabStatusData(filter: $filter) {
      summary {
        totalLabRemaining
        totalLabDispensed
        totalLabPurchased
        name
        totalLabDispensedCostAmount
        totalLabDispensedSalesAmount
        totalLabPurchasedCostAmount
        totalLabPurchasedSalesAmount
        totalLabRemainingSalesAmount
        totalLabRemainingCostAmount
      }
      details {
        dateAdded
        itemName
        addedBy
        totalQuantityPurchased
        totalQuantityDispensed
        totalQuantityRemaining
        totalQuantityExpired
        totalQuantityDamaged
        supplier
        totalCosts
        totalSales
        totalAverageCost
        totalExpiredAmount
        totalDamagedAmount
        restockLevel
      }
    }
  }
`;

export const GET_DAMAGED_DRUGS = gql`
  query GetDamagedDrugs($filter: StocksAnalyticsFilter!) {
    getDamagedDrugs(filter: $filter) {
      name
      totalDamaged
      itemName
      date
      unitCost
      totalCost
      totalDamagedAmount
    }
  }
`;

export const GET_EXPIRED_DRUGS = gql`
  query GetExpiredDrugs($filter: StocksAnalyticsFilter!) {
    getExpiredDrugs(filter: $filter) {
      name
      totalRemaining
      itemName
      date
      unitCost
      totalCost
      totalExpiredAmount
    }
  }
`;

export const GET_EXPIRED_CONSUMABLES = gql`
  query GetExpiredConsumables($filter: StocksAnalyticsFilter!) {
    getExpiredConsumables(filter: $filter) {
      name
      totalRemaining
      itemName
      date
      unitCost
      totalCost
      totalExpiredAmount
    }
  }
`;

export const GET_EXPIRED_REAGENTS = gql`
  query GetExpiredReagents($filter: StocksAnalyticsFilter!) {
    getExpiredReagents(filter: $filter) {
      name
      totalRemaining
      itemName
      date
      unitCost
      totalCost
      totalExpiredAmount
    }
  }
`;
export const GET_STOCKS_DATA = gql`
  query GetStocksData($filter: StocksAnalyticsFilter) {
    getStocksData(filter: $filter) {
      ...StocksSummary
      name
    }
  }
  ${STOCKS_SUMMARY}
`;

export const GET_ROOM_DATA = gql`
  query GetRoomSummary($filter: StocksAnalyticsFilter) {
    getRoomSummary(filter: $filter) {
      totalRoomAvailable
      totalRoomOccupied
      name
    }
  }
`;

export const GET_CONSUMABLE_DATA = gql`
  query GetConsumableStatusData($filter: StocksAnalyticsFilter) {
    getConsumableStatusData(filter: $filter) {
      summary {
        name
        totalConsumableConsumed
        totalConsumableRemaining
        totalConsumablePurchased
        totalConsumableDamaged
        totalConsumableExpired

        totalRemainingCostAmount
        totalRemainingSalesAmount
        totalPurchasedCostAmount
        totalPurchasedSalesAmount
        totalDispensedCostAmount
        totalDispensedSalesAmount
        totalDamagedCostAmount
        totalDamagedSalesAmount
        totalExpiredSalesAmount
        totalExpiredCostAmount
      }

      details {
        dateAdded
        itemName
        addedBy
        totalQuantityPurchased
        totalQuantityDispensed
        totalQuantityRemaining
        totalQuantityExpired
        totalQuantityDamaged
        supplier
        totalCosts
        totalSales
        totalAverageCost
        totalExpiredAmount
        totalDamagedAmount
        restockLevel
        totalAverageCost
        totalExpiredAmount
        totalDamagedAmount
        restockLevel
      }
    }
  }
`;

export const GET_DEVICE_DATA = gql`
  query GetDeviceStatusData($filter: StocksAnalyticsFilter) {
    getDeviceStatusData(filter: $filter) {
      summary {
        totalDeviceNotInUse
        totalDeviceInUse
        totalDevices
        name
        totalDeviceInUseCostAmount
        totalDeviceNotInUseCostAmount
        totalDeviceInUseSalesAmount
        totalDeviceNotInUseSalesAmount

        totalQuantityAvailable
        totalQuantityAvailableCostAmount
        totalQuantityAvailableSalesAmount
        totalQuantityExpired
        totalQuantityExpiredCostAmount
        totalQuantityExpiredSalesAmount
      }

      details {
        dateAdded
        itemName
        addedBy
        totalQuantityPurchased
        totalQuantityDispensed
        totalQuantityRemaining
        totalQuantityExpired
        totalQuantityDamaged
        totalQuantityAvailable
        totalQuantityInUse
        totalQuantityNotInUse
        supplier
        totalCosts
        totalSales
        totalAverageCost
        totalExpiredAmount
        totalDamagedAmount
        restockLevel
      }
    }
  }
`;

export const GET_VEHICLE_DATA = gql`
  query GetVehicleStatusData($filter: StocksAnalyticsFilter) {
    getVehicleStatusData(filter: $filter) {
      summary {
        name
        totalVehicleNotInService
        totalVehicleInService
        totalQuantityPurchased
        totalQuantityExpired
        totalVehicleInServiceCostAmount
        totalVehicleNotInServiceCostAmount
        totalVehicleInServiceSalesAmount
        totalVehicleNotInServiceSalesAmount
        totalQuantityExpiredCostAmount
        totalQuantityExpiredSalesAmount
        totalQuantityPurchasedCostAmount
        totalQuantityPurchasedSalesAmount
      }
      details {
        dateAdded
        itemName
        addedBy
        totalQuantityPurchased
        totalQuantityDispensed
        totalQuantityRemaining
        totalQuantityExpired
        totalQuantityDamaged
        totalQuantityInService
        totalQuantityNotInService
        supplier
        totalCosts
        totalSales
        totalAverageCost
        totalExpiredAmount
        totalDamagedAmount
        restockLevel
      }
    }
  }
`;

export const GET_DRUGS_DATA = gql`
  query GetDrugStatusData($filter: StocksAnalyticsFilter) {
    getDrugStatusData(filter: $filter) {
      summary {
        name
        totalDrugRemaining
        totalDrugDispensed
        totalDrugPurchased
        totalDrugDamaged
        totalDrugExpired

        totalRemainingCostAmount
        totalRemainingSalesAmount
        totalPurchasedCostAmount
        totalPurchasedSalesAmount
        totalDispensedCostAmount
        totalDispensedSalesAmount
        totalDamagedCostAmount
        totalDamagedSalesAmount
        totalExpiredSalesAmount
        totalExpiredCostAmount
      }
      details {
        dateAdded
        itemName
        addedBy
        totalQuantityPurchased
        totalQuantityDispensed
        totalQuantityRemaining
        totalQuantityExpired
        totalQuantityDamaged
        supplier
        totalCosts
        totalSales
        totalAverageCost
        totalExpiredAmount
        totalDamagedAmount
        restockLevel
        totalAverageCost
        totalExpiredAmount
        totalDamagedAmount
        restockLevel
      }
    }
  }
`;

export const GET_TOPSTOCKS_DATA = gql`
  query TopStocksSummary($filter: StocksAnalyticsFilter) {
    topStocksSummary(filter: $filter) {
      mostPurchasedDrugs {
        count
        itemName
        totalRevenue
        totalQuantityRemaining
      }
      mostPurchasedDevices {
        count
        itemName
        totalRevenue
        totalQuantityRemaining
      }
      mostPurchasedVehicles {
        count
        itemName
        totalRevenue
        totalQuantityRemaining
      }
      mostPurchasedConsumables {
        count
        itemName
        totalRevenue
        totalQuantityRemaining
      }
      topSuppliers {
        count
        supplier
        totalRevenue
        totalQuantityRemaining
      }
      topLabs {
        count
        itemName
        totalRevenue
        totalQuantityRemaining
      }
    }
  }
`;

export const GET_HMO_ANALYTICS_DATA = gql`
  query GetHmosAnalyticsData($filter: HmosAnalyticsFilter) {
    getHmosData(filter: $filter) {
      summary {
        hmoProvider
        hospitalName
        name
        totalEnrollees
        totalEnrolleeVisitations
        totalActiveEnrollees
        totalInactiveEnrollees
        totalApprovedClaims
        totalClaims
        totalDraftClaims
        totalRejectedClaims
        totalSubmittedClaims
        totalClaimsAmount
        totalApprovedClaimsAmount
        totalRejectedClaimsAmount
        totalDraftClaimsAmount
        totalSubmittedClaimsAmount
        totalFlaggedClaims
        totalPaidClaims
        totalFlaggedClaimsAmount
        totalPaidClaimsAmount
        totalPendingClaims
        totalPendingClaimsAmount

        totalClaimOfficerApprovedClaims
        totalClaimOfficerHODApprovedClaims
        totalClaimReviewerApprovedClaims
        totalClaimReviewerHODApprovedClaims
        totalClaimAuditApprovedClaims
        totalClaimAuditHODApprovedClaims
        totalClaimAdminApprovedClaims
        totalClaimFinanceApprovedClaims
        totalClaimAccountApprovedClaims
        totalClaimOfficerApprovedClaimsAmount
        totalClaimOfficerHODApprovedClaimsAmount
        totalClaimReviewerApprovedClaimsAmount
        totalClaimReviewerHODApprovedClaimsAmount
        totalClaimAuditApprovedClaimsAmount
        totalClaimAuditHODApprovedClaimsAmount
        totalClaimAdminApprovedClaimsAmount
        totalClaimFinanceApprovedClaimsAmount
        totalClaimAccountApprovedClaimsAmount

        totalClaimOfficerRejectedClaims
        totalClaimOfficerHODRejectedClaims
        totalClaimReviewerRejectedClaims
        totalClaimReviewerHODRejectedClaims
        totalClaimAuditRejectedClaims
        totalClaimAuditHODRejectedClaims
        totalClaimAdminRejectedClaims
        totalClaimFinanceRejectedClaims
        totalClaimAccountRejectedClaims
        totalClaimOfficerRejectedClaimsAmount
        totalClaimOfficerHODRejectedClaimsAmount
        totalClaimReviewerRejectedClaimsAmount
        totalClaimReviewerHODRejectedClaimsAmount
        totalClaimAuditRejectedClaimsAmount
        totalClaimAuditHODRejectedClaimsAmount
        totalClaimAdminRejectedClaimsAmount
        totalClaimFinanceRejectedClaimsAmount
        totalClaimAccountRejectedClaimsAmount

        totalClaimOfficerPendingClaims
        totalClaimOfficerHODPendingClaims
        totalClaimReviewerPendingClaims
        totalClaimReviewerHODPendingClaims
        totalClaimAuditPendingClaims
        totalClaimAuditHODPendingClaims
        totalClaimAdminPendingClaims
        totalClaimFinancePendingClaims
        totalClaimAccountPendingClaims
        totalClaimOfficerPendingClaimsAmount
        totalClaimOfficerHODPendingClaimsAmount
        totalClaimReviewerPendingClaimsAmount
        totalClaimReviewerHODPendingClaimsAmount
        totalClaimAuditPendingClaimsAmount
        totalClaimAuditHODPendingClaimsAmount
        totalClaimAdminPendingClaimsAmount
        totalClaimFinancePendingClaimsAmount
        totalClaimAccountPendingClaimsAmount
        totalClaimConfirmationPendingClaims
        totalClaimConfirmationPendingClaimsAmount

        totalClaimOfficerFlaggedClaims
        totalClaimOfficerHODFlaggedClaims
        totalClaimReviewerFlaggedClaims
        totalClaimReviewerHODFlaggedClaims
        totalClaimAuditFlaggedClaims
        totalClaimAuditHODFlaggedClaims
        totalClaimAdminFlaggedClaims
        totalClaimFinanceFlaggedClaims
        totalClaimAccountFlaggedClaims
        totalClaimOfficerFlaggedClaimsAmount
        totalClaimOfficerHODFlaggedClaimsAmount
        totalClaimReviewerFlaggedClaimsAmount
        totalClaimReviewerHODFlaggedClaimsAmount
        totalClaimAuditFlaggedClaimsAmount
        totalClaimAuditHODFlaggedClaimsAmount
        totalClaimAdminFlaggedClaimsAmount
        totalClaimFinanceFlaggedClaimsAmount
        totalClaimAccountFlaggedClaimsAmount
        totalClaimConfirmationFlaggedClaims
        totalClaimConfirmationFlaggedClaimsAmount

        totalClaimOfficerStaffCount
        totalClaimOfficerHODStaffCount
        totalClaimReviewerStaffCount
        totalClaimReviewerHODStaffCount
        totalClaimAuditStaffCount
        totalClaimAuditHODStaffCount
        totalClaimAdminStaffCount
        totalClaimFinanceStaffCount
        totalClaimAccountStaffCount
        totalClaimConfirmationStaffCount
      }
      detailedData {
        hospitalName
        treatmentDateTime
        treatmentStartDate
        treatmentEndDate
        visitationType
        enrolleeNumber
        enrolleeName
        serviceTypeName
        claimStatus
        claimId
        batchNumber
        totalQuantity
        amountSubmitted
        amountApproved
        amountRejected
        submittedBy
        approvedBy
        rejectedBy
        clinicalDiagnosis
        flaggedBy
        memberPlan
        companyName
        reasonForRejection
        amountPaid
        causeOfDeath
        timeOfDeath
        birthCount
        deliveryDateTime
        age
        gender
        memberPlanGroup
        memberPlanSubGroup
        lga
        ward
        utilizationStatus {
          status
          vettingGroup
        }
      }
      automaticAndManualData {
        totalAutomated
        totalManual
        byRoles {
          name
          count
          manualCount
        }
        byStaffs {
          name
          count
          type
          manualCount
        }
      }
      totalMaleDeath
      totalFemaleDeath
      deathAgeRanges {
        count
        category
      }
    }
  }
`;

export const GET_HMO_ANALYTICS_SUMMARY = gql`
  query GetHmoAnalyticsSummary($filter: HmosAnalyticsFilter) {
    getHmosSummary(filter: $filter) {
      groupedData {
        hospitalId
        hmoProvider
        hospitalName
        totalClaims
        totalApprovedClaims
        totalRejectedClaims
        totalDraftClaims
        totalSubmittedClaims
        totalClaimsAmount
        totalApprovedClaimsAmount
        totalRejectedClaimsAmount
        totalDraftClaimsAmount
        totalSubmittedClaimsAmount
        totalFlaggedClaims
        totalPaidClaims
        totalEnrollees
        totalEnrolleeVisitations
        totalActiveEnrollees
        totalInactiveEnrollees
        totalFlaggedClaimsAmount
        totalPaidClaimsAmount
        totalPendingClaims
        totalPendingClaimsAmount

        totalClaimOfficerApprovedClaims
        totalClaimOfficerHODApprovedClaims
        totalClaimReviewerApprovedClaims
        totalClaimReviewerHODApprovedClaims
        totalClaimAuditApprovedClaims
        totalClaimAuditHODApprovedClaims
        totalClaimAdminApprovedClaims
        totalClaimFinanceApprovedClaims
        totalClaimAccountApprovedClaims
        totalClaimOfficerApprovedClaimsAmount
        totalClaimOfficerHODApprovedClaimsAmount
        totalClaimReviewerApprovedClaimsAmount
        totalClaimReviewerHODApprovedClaimsAmount
        totalClaimAuditApprovedClaimsAmount
        totalClaimAuditHODApprovedClaimsAmount
        totalClaimAdminApprovedClaimsAmount
        totalClaimFinanceApprovedClaimsAmount
        totalClaimAccountApprovedClaimsAmount

        totalClaimOfficerRejectedClaims
        totalClaimOfficerHODRejectedClaims
        totalClaimReviewerRejectedClaims
        totalClaimReviewerHODRejectedClaims
        totalClaimAuditRejectedClaims
        totalClaimAuditHODRejectedClaims
        totalClaimAdminRejectedClaims
        totalClaimFinanceRejectedClaims
        totalClaimAccountRejectedClaims
        totalClaimOfficerRejectedClaimsAmount
        totalClaimOfficerHODRejectedClaimsAmount
        totalClaimReviewerRejectedClaimsAmount
        totalClaimReviewerHODRejectedClaimsAmount
        totalClaimAuditRejectedClaimsAmount
        totalClaimAuditHODRejectedClaimsAmount
        totalClaimAdminRejectedClaimsAmount
        totalClaimFinanceRejectedClaimsAmount
        totalClaimAccountRejectedClaimsAmount

        totalClaimOfficerPendingClaims
        totalClaimOfficerHODPendingClaims
        totalClaimReviewerPendingClaims
        totalClaimReviewerHODPendingClaims
        totalClaimAuditPendingClaims
        totalClaimAuditHODPendingClaims
        totalClaimAdminPendingClaims
        totalClaimFinancePendingClaims
        totalClaimAccountPendingClaims
        totalClaimOfficerPendingClaimsAmount
        totalClaimOfficerHODPendingClaimsAmount
        totalClaimReviewerPendingClaimsAmount
        totalClaimReviewerHODPendingClaimsAmount
        totalClaimAuditPendingClaimsAmount
        totalClaimAuditHODPendingClaimsAmount
        totalClaimAdminPendingClaimsAmount
        totalClaimFinancePendingClaimsAmount
        totalClaimAccountPendingClaimsAmount
        totalClaimConfirmationPendingClaims
        totalClaimConfirmationPendingClaimsAmount

        totalClaimOfficerFlaggedClaims
        totalClaimOfficerHODFlaggedClaims
        totalClaimReviewerFlaggedClaims
        totalClaimReviewerHODFlaggedClaims
        totalClaimAuditFlaggedClaims
        totalClaimAuditHODFlaggedClaims
        totalClaimAdminFlaggedClaims
        totalClaimFinanceFlaggedClaims
        totalClaimAccountFlaggedClaims
        totalClaimOfficerFlaggedClaimsAmount
        totalClaimOfficerHODFlaggedClaimsAmount
        totalClaimReviewerFlaggedClaimsAmount
        totalClaimReviewerHODFlaggedClaimsAmount
        totalClaimAuditFlaggedClaimsAmount
        totalClaimAuditHODFlaggedClaimsAmount
        totalClaimAdminFlaggedClaimsAmount
        totalClaimFinanceFlaggedClaimsAmount
        totalClaimAccountFlaggedClaimsAmount
        totalClaimConfirmationFlaggedClaims
        totalClaimConfirmationFlaggedClaimsAmount

        totalClaimOfficerStaffCount
        totalClaimOfficerHODStaffCount
        totalClaimReviewerStaffCount
        totalClaimReviewerHODStaffCount
        totalClaimAuditStaffCount
        totalClaimAuditHODStaffCount
        totalClaimAdminStaffCount
        totalClaimFinanceStaffCount
        totalClaimAccountStaffCount
        totalClaimConfirmationStaffCount
      }
      automaticAndManualData {
        totalAutomated
        totalManual
        byRoles {
          name
          count
          manualCount
        }
        byStaffs {
          name
          count
          type
          manualCount
        }
      }
    }
  }
`;

export const GET_HMO_PREAUTHORIZATIONS_DATA = gql`
  query GetHmoPreauthorizationsData($filter: HmosAnalyticsFilter) {
    getHmoPreauthorizationsData(filter: $filter) {
      summary {
        name
        hmoProvider
        hospitalName
        totalEnrollees
        totalEnrolleeVisitations
        totalActiveEnrollees
        totalInactiveEnrollees
        totalPreauthorizations
        totalPendingPreauthorizations
        totalApprovedPreauthorizations
        totalRejectedPreauthorizations
        totalPreauthorizationAmount
        totalApprovedPreauthorizationAmount
        totalRejectedPreauthorizationAmount
        totalPendingPreauthorizationAmount
        totalReferrals
        totalReferralsAmount
        totalPendingReferrals
        totalPendingReferralsAmount
        totalApprovedReferrals
        totalApprovedReferralsAmount
        totalRejectedReferrals
        totalRejectedReferralsAmount
        totalFlaggedPreauthorizations
        totalFlaggedPreauthorizationsAmount

        totalClaimConfirmationApprovedPreauthorizations
        totalClaimConfirmationApprovedPreauthorizationsAmount
        totalClaimOfficerApprovedPreauthorizations
        totalClaimOfficerApprovedPreauthorizationsAmount
        totalClaimOfficerHODApprovedPreauthorizations
        totalClaimOfficerHODApprovedPreauthorizationsAmount
        totalClaimReviewerApprovedPreauthorizations
        totalClaimReviewerApprovedPreauthorizationsAmount
        totalClaimReviewerHODApprovedPreauthorizations
        totalClaimReviewerHODApprovedPreauthorizationsAmount
        totalClaimAuditApprovedPreauthorizations
        totalClaimAuditApprovedPreauthorizationsAmount
        totalClaimAuditHODApprovedPreauthorizations
        totalClaimAuditHODApprovedPreauthorizationsAmount
        totalClaimAdminApprovedPreauthorizations
        totalClaimAdminApprovedPreauthorizationsAmount
        totalClaimFinanceApprovedPreauthorizations
        totalClaimFinanceApprovedPreauthorizationsAmount
        totalClaimAccountApprovedPreauthorizations
        totalClaimAccountApprovedPreauthorizationsAmount
        totalClaimAgentApprovedPreauthorizations
        totalClaimAgentApprovedPreauthorizationsAmount
        totalClaimAgentHODApprovedPreauthorizations
        totalClaimAgentHODApprovedPreauthorizationsAmount

        totalClaimConfirmationRejectedPreauthorizations
        totalClaimConfirmationRejectedPreauthorizationsAmount
        totalClaimOfficerRejectedPreauthorizations
        totalClaimOfficerRejectedPreauthorizationsAmount
        totalClaimOfficerHODRejectedPreauthorizations
        totalClaimOfficerHODRejectedPreauthorizationsAmount
        totalClaimReviewerRejectedPreauthorizations
        totalClaimReviewerRejectedPreauthorizationsAmount
        totalClaimReviewerHODRejectedPreauthorizations
        totalClaimReviewerHODRejectedPreauthorizationsAmount
        totalClaimAuditRejectedPreauthorizations
        totalClaimAuditRejectedPreauthorizationsAmount
        totalClaimAuditHODRejectedPreauthorizations
        totalClaimAuditHODRejectedPreauthorizationsAmount
        totalClaimAdminRejectedPreauthorizations
        totalClaimAdminRejectedPreauthorizationsAmount
        totalClaimFinanceRejectedPreauthorizations
        totalClaimFinanceRejectedPreauthorizationsAmount
        totalClaimAccountRejectedPreauthorizations
        totalClaimAccountRejectedPreauthorizationsAmount
        totalClaimAgentRejectedPreauthorizations
        totalClaimAgentRejectedPreauthorizationsAmount
        totalClaimAgentHODRejectedPreauthorizations
        totalClaimAgentHODRejectedPreauthorizationsAmount

        totalClaimConfirmationFlaggedPreauthorizations
        totalClaimConfirmationFlaggedPreauthorizationsAmount
        totalClaimOfficerFlaggedPreauthorizations
        totalClaimOfficerFlaggedPreauthorizationsAmount
        totalClaimOfficerHODFlaggedPreauthorizations
        totalClaimOfficerHODFlaggedPreauthorizationsAmount
        totalClaimReviewerFlaggedPreauthorizations
        totalClaimReviewerFlaggedPreauthorizationsAmount
        totalClaimReviewerHODFlaggedPreauthorizations
        totalClaimReviewerHODFlaggedPreauthorizationsAmount
        totalClaimAuditFlaggedPreauthorizations
        totalClaimAuditFlaggedPreauthorizationsAmount
        totalClaimAuditHODFlaggedPreauthorizations
        totalClaimAuditHODFlaggedPreauthorizationsAmount
        totalClaimAdminFlaggedPreauthorizations
        totalClaimAdminFlaggedPreauthorizationsAmount
        totalClaimFinanceFlaggedPreauthorizations
        totalClaimFinanceFlaggedPreauthorizationsAmount
        totalClaimAccountFlaggedPreauthorizations
        totalClaimAccountFlaggedPreauthorizationsAmount
        totalClaimAgentFlaggedPreauthorizations
        totalClaimAgentFlaggedPreauthorizationsAmount
        totalClaimAgentHODFlaggedPreauthorizations
        totalClaimAgentHODFlaggedPreauthorizationsAmount
      }
      detailedData {
        hospitalName
        hmoProvider
        treatmentDateTime
        treatmentStartDate
        treatmentEndDate
        visitationType
        enrolleeNumber
        enrolleeName
        serviceTypeName
        preauthorizationStatus
        preauthorizationCode
        totalQuantity
        amountRequested
        requestedBy
        amountApproved
        approvedBy
        amountRejected
        rejectedBy
        clinicalDiagnosis
        causeOfDeath
        timeOfDeath
        birthCount
        deliveryDateTime
        age
        gender
        memberPlan
        memberPlanGroup
        memberPlanSubGroup
        lga
        ward
        responseDateTime
        createdDate
      }
      referralData {
        treatmentDateTime
        enrolleeNumber
        enrolleeName
        referralCode
        referralStatus
        referredBy
        referringFacility
        referredToFacility
        visitType
        priority
        clinicalDiagnosis
        approvedBy
        rejectedBy
        age
        gender
        memberPlan
        memberPlanGroup
        memberPlanSubGroup
        providerReferralRemarks
        lga
        ward
      }
      automaticAndManualData {
        totalAutomated
        totalManual
        byRoles {
          name
          count
          manualCount
        }
        byStaffs {
          name
          count
          type
          manualCount
        }
      }
      totalClaimOfficerStaffCount
      totalClaimOfficerHODStaffCount
      totalClaimReviewerStaffCount
      totalClaimReviewerHODStaffCount
      totalClaimAuditStaffCount
      totalClaimAuditHODStaffCount
      totalClaimAdminStaffCount
      totalClaimFinanceStaffCount
      totalClaimAccountStaffCount
      totalClaimAgentStaffCount
      totalClaimAgentHODStaffCount
      totalClaimConfirmationStaffCount
      totalMaleDeath
      totalFemaleDeath
      deathAgeRanges {
        count
        category
      }
    }
  }
`;

export const GET_HMO_PREAUTHORIZATIONS_SUMMARY = gql`
  query GetHmoPreauthorizationsSummary($filter: HmosAnalyticsFilter) {
    getHmoPreauthorizationsSummary(filter: $filter) {
      groupedData {
        hospitalName
        hmoProvider
        totalPreauthorizations
        totalPendingPreauthorizations
        totalApprovedPreauthorizations
        totalRejectedPreauthorizations
        totalPreauthorizationAmount
        totalRejectedPreauthorizationAmount
        totalPendingPreauthorizationAmount
        totalApprovedPreauthorizationAmount
        totalEnrollees
        totalEnrolleeVisitations
        totalActiveEnrollees
        totalInactiveEnrollees
        totalReferrals
        totalReferralsAmount
        totalPendingReferrals
        totalPendingReferralsAmount
        totalApprovedReferrals
        totalApprovedReferralsAmount
        totalRejectedReferrals
        totalRejectedReferralsAmount
        totalFlaggedPreauthorizations
        totalFlaggedPreauthorizationsAmount

        totalClaimConfirmationApprovedPreauthorizations
        totalClaimConfirmationApprovedPreauthorizationsAmount
        totalClaimOfficerApprovedPreauthorizations
        totalClaimOfficerApprovedPreauthorizationsAmount
        totalClaimOfficerHODApprovedPreauthorizations
        totalClaimOfficerHODApprovedPreauthorizationsAmount
        totalClaimReviewerApprovedPreauthorizations
        totalClaimReviewerApprovedPreauthorizationsAmount
        totalClaimReviewerHODApprovedPreauthorizations
        totalClaimReviewerHODApprovedPreauthorizationsAmount
        totalClaimAuditApprovedPreauthorizations
        totalClaimAuditApprovedPreauthorizationsAmount
        totalClaimAuditHODApprovedPreauthorizations
        totalClaimAuditHODApprovedPreauthorizationsAmount
        totalClaimAdminApprovedPreauthorizations
        totalClaimAdminApprovedPreauthorizationsAmount
        totalClaimFinanceApprovedPreauthorizations
        totalClaimFinanceApprovedPreauthorizationsAmount
        totalClaimAccountApprovedPreauthorizations
        totalClaimAccountApprovedPreauthorizationsAmount
        totalClaimAgentApprovedPreauthorizations
        totalClaimAgentApprovedPreauthorizationsAmount
        totalClaimAgentHODApprovedPreauthorizations
        totalClaimAgentHODApprovedPreauthorizationsAmount

        totalClaimConfirmationRejectedPreauthorizations
        totalClaimConfirmationRejectedPreauthorizationsAmount
        totalClaimOfficerRejectedPreauthorizations
        totalClaimOfficerRejectedPreauthorizationsAmount
        totalClaimOfficerHODRejectedPreauthorizations
        totalClaimOfficerHODRejectedPreauthorizationsAmount
        totalClaimReviewerRejectedPreauthorizations
        totalClaimReviewerRejectedPreauthorizationsAmount
        totalClaimReviewerHODRejectedPreauthorizations
        totalClaimReviewerHODRejectedPreauthorizationsAmount
        totalClaimAuditRejectedPreauthorizations
        totalClaimAuditRejectedPreauthorizationsAmount
        totalClaimAuditHODRejectedPreauthorizations
        totalClaimAuditHODRejectedPreauthorizationsAmount
        totalClaimAdminRejectedPreauthorizations
        totalClaimAdminRejectedPreauthorizationsAmount
        totalClaimFinanceRejectedPreauthorizations
        totalClaimFinanceRejectedPreauthorizationsAmount
        totalClaimAccountRejectedPreauthorizations
        totalClaimAccountRejectedPreauthorizationsAmount
        totalClaimAgentRejectedPreauthorizations
        totalClaimAgentRejectedPreauthorizationsAmount
        totalClaimAgentHODRejectedPreauthorizations
        totalClaimAgentHODRejectedPreauthorizationsAmount

        totalClaimConfirmationFlaggedPreauthorizations
        totalClaimConfirmationFlaggedPreauthorizationsAmount
        totalClaimOfficerFlaggedPreauthorizations
        totalClaimOfficerFlaggedPreauthorizationsAmount
        totalClaimOfficerHODFlaggedPreauthorizations
        totalClaimOfficerHODFlaggedPreauthorizationsAmount
        totalClaimReviewerFlaggedPreauthorizations
        totalClaimReviewerFlaggedPreauthorizationsAmount
        totalClaimReviewerHODFlaggedPreauthorizations
        totalClaimReviewerHODFlaggedPreauthorizationsAmount
        totalClaimAuditFlaggedPreauthorizations
        totalClaimAuditFlaggedPreauthorizationsAmount
        totalClaimAuditHODFlaggedPreauthorizations
        totalClaimAuditHODFlaggedPreauthorizationsAmount
        totalClaimAdminFlaggedPreauthorizations
        totalClaimAdminFlaggedPreauthorizationsAmount
        totalClaimFinanceFlaggedPreauthorizations
        totalClaimFinanceFlaggedPreauthorizationsAmount
        totalClaimAccountFlaggedPreauthorizations
        totalClaimAccountFlaggedPreauthorizationsAmount
        totalClaimAgentFlaggedPreauthorizations
        totalClaimAgentFlaggedPreauthorizationsAmount
        totalClaimAgentHODFlaggedPreauthorizations
        totalClaimAgentHODFlaggedPreauthorizationsAmount
      }
      automaticAndManualData {
        totalAutomated
        totalManual
        byRoles {
          name
          count
          manualCount
        }
        byStaffs {
          name
          count
          type
          manualCount
        }
      }
      totalClaimOfficerStaffCount
      totalClaimOfficerHODStaffCount
      totalClaimReviewerStaffCount
      totalClaimReviewerHODStaffCount
      totalClaimAuditStaffCount
      totalClaimAuditHODStaffCount
      totalClaimAdminStaffCount
      totalClaimFinanceStaffCount
      totalClaimAccountStaffCount
      totalClaimAgentStaffCount
      totalClaimAgentHODStaffCount
      totalClaimConfirmationStaffCount
    }
  }
`;

export const GET_DELIVERIES_BY_METHOD = gql`
  query GetDeliveriesByMethod($filter: MaternalHealthAnalyticsFilter) {
    getDeliveriesByMethod(filter: $filter) {
      name
      totalDeliveriesByMethod
      category
    }
  }
`;

// getPregnantWomenCounselled
export const GET_PREGNANT_WOMEN_COUNSELLED = gql`
  query GetPregnantWomenCounselled($filter: MaternalHealthAnalyticsFilter) {
    getPregnantWomenCounselled(filter: $filter) {
      name
      totalPregnantWomenCounselled
      totalCounselledFGM
      totalCounselledFamilyPlanning
      totalCounselledMaternalNutrition
      totalCounselledLabourBirthPreparedness
      category
    }
  }
`;

// getAntenatalVisits
export const GET_ANTENATAL_VISITS = gql`
  query GetAntenatalVisits($filter: MaternalHealthAnalyticsFilter) {
    getAntenatalVisits(filter: $filter) {
      name
      totalAntenatalVisits
      antenatalAttendance {
        count
        category
      }
      ageRanges {
        count
        category
      }
    }
  }
`;

// getAbortionsByMethod
export const GET_ABORTIONS_BY_METHOD = gql`
  query GetAbortionsByMethod($filter: MaternalHealthAnalyticsFilter) {
    getAbortionsByMethod(filter: $filter) {
      name
      totalAbortionByMethod
      abortionMethod {
        count
        category
      }
    }
  }
`;

// getPostnatalVisits
export const GET_POSTNATAL_VISITS = gql`
  query GetPostnatalVisits($filter: MaternalHealthAnalyticsFilter) {
    getPostnatalVisits(filter: $filter) {
      name
      totalPostnatalVisits
    }
  }
`;

export const GET_STILL_BIRTHS = gql`
  query GetStillBirths($filter: MaternalHealthAnalyticsFilter) {
    getStillBirths(filter: $filter) {
      name
      category
      count
    }
  }
`;

export const GET_LIVE_BIRTHS = gql`
  query GetLiveBirths($filter: MaternalHealthAnalyticsFilter) {
    getLiveBirths(filter: $filter) {
      name
      totalLiveBirths
      birthByGender {
        count
        category
      }
      birthByWeight {
        count
        category
      }
    }
  }
`;

export const GET_NEW_FAMILY_PLANNING_ACCEPTORS = gql`
  query GetNewFamilyPlanningAcceptors($filter: FamilyPlanningAnalyticsFilter) {
    getNewFamilyPlanningAcceptors(filter: $filter) {
      name
      totalNewFamilyPlanningAcceptors
      category
    }
  }
`;

export const GET_CLIENT_COUNSELLED = gql`
  query GetClientCounselled($filter: FamilyPlanningAnalyticsFilter) {
    getClientCounselled(filter: $filter) {
      name
      totalClientCounselled
      category
    }
  }
`;

export const GET_WOMEN_COUNSELLED_ON_POSTPASTUM_FP = gql`
  query GetWomenCounselledOnPostPartumFP($filter: FamilyPlanningAnalyticsFilter) {
    getWomenCounselledOnPostPartumFamilyPlanning(filter: $filter) {
      name
      totalClientCounselled
    }
  }
`;

export const GET_FEMALES_USING_MODERN_CONTRACEPTIONS = gql`
  query GetFemalesUsingModernContraceptions($filter: FamilyPlanningAnalyticsFilter) {
    getFemalesUsingModernContraceptions(filter: $filter) {
      name
      totalFemalesUsingModernContraceptions
      ageRanges {
        count
        category
      }
    }
  }
`;

export const GET_VOLUNTARY_STERILIZATION = gql`
  query GetVoluntarySterilization($filter: FamilyPlanningAnalyticsFilter) {
    getVoluntarySterilization(filter: $filter) {
      name
      totalVoluntarySterilization
      category
    }
  }
`;

export const GET_PILLS_GIVEN = gql`
  query GetPillsGiven($filter: FamilyPlanningAnalyticsFilter) {
    getPillsGiven(filter: $filter) {
      name
      totalPillsGiven
      category
    }
  }
`;

export const GET_CLIENTS_THAT_RECEIVED_CONDOMS = gql`
  query GetClientsThatReceivedCondoms($filter: FamilyPlanningAnalyticsFilter) {
    getClientsThatReceivedCondoms(filter: $filter) {
      name
      totalClientsThatReceivedCondoms
      category
    }
  }
`;

export const GET_INJECTABLES_GIVEN = gql`
  query GetInjectablesGiven($filter: FamilyPlanningAnalyticsFilter) {
    getInjectablesGiven(filter: $filter) {
      name
      totalInjectablesGiven
      category
    }
  }
`;

export const GET_IUD_INSERTED = gql`
  query GetIUDInserted($filter: FamilyPlanningAnalyticsFilter) {
    getIUDInserted(filter: $filter) {
      name
      totalIUDInserted
      category
    }
  }
`;

export const GET_IMPLANTS_INSERTED = gql`
  query GetImplantsInserted($filter: FamilyPlanningAnalyticsFilter) {
    getImplantsInserted(filter: $filter) {
      name
      totalImplantsInserted
      category
    }
  }
`;

// getMalnutritionCases
export const GET_MALNUTRITION_CASES = gql`
  query GetMalnutritionCases($filter: CasesAnalyticsFilter) {
    getMalnutritionCases(filter: $filter) {
      name
      totalFemale
      totalMale
      ageRanges {
        category
        count
      }
    }
  }
`;

export const GET_SEVERE_ACUTE_MALNUTRITION_CASES = gql`
  query GetSevereAcuteMalnutritionCases($filter: CasesAnalyticsFilter) {
    getSevereAcuteMalnutritionCases(filter: $filter) {
      name
      totalFemale
      totalMale
      ageRanges {
        category
        count
      }
    }
  }
`;

// getWomenAdmittedWithObstetricFistula
export const GET_WOMEN_ADMITTED_WITH_OBSTETRIC_FISTULA = gql`
  query GetWomenAdmittedWithObstetricFistula($filter: CasesAnalyticsFilter) {
    getWomenAdmittedWithObstetricFistula(filter: $filter) {
      name
      totalVVF
      totalRVF
      ageRanges {
        category
        count
      }
    }
  }
`;

// getWomenDischargedAfterSurgeryForObstetricFistula
export const GET_WOMEN_DISCHARDED_AFTER_SURGERY_FOR_OBSTETRIC_FISTULA = gql`
  query GetWomenDischargedAfterSurgeryForObstetricFistula($filter: CasesAnalyticsFilter) {
    getWomenDischargedAfterSurgeryForObstetricFistula(filter: $filter) {
      name
      totalVVF
      totalRVF
      ageRanges {
        category
        count
      }
    }
  }
`;

// getWomenReceivingSurgeryForObstetricFistulaRepair
export const GET_WOMEN_RECEIVING_SURGERY_FOR_OBSTETRIC_FISTULA_REPAIR = gql`
  query GetWomenReceivingSurgeryForObstetricFistulaRepair($filter: CasesAnalyticsFilter) {
    getWomenReceivingSurgeryForObstetricFistulaRepair(filter: $filter) {
      name
      totalVVF
      totalRVF
      ageRanges {
        category
        count
      }
    }
  }
`;
// getGenderBasedViolenceCases
export const GET_GENDER_BASED_VIOLENCE_CASES = gql`
  query GetGenderBasedViolenceCases($filter: CasesAnalyticsFilter) {
    getGenderBasedViolenceCases(filter: $filter) {
      name
      totalFemale
      totalMale
      ageRanges {
        category
        count
      }
    }
  }
`;

// getHepatitisBTesting
export const GET_HEPATITIS_B_TESTING = gql`
  query GetHepatitisBTesting($filter: CasesAnalyticsFilter) {
    getHepatitisBTesting(filter: $filter) {
      name
      totalFemale
      totalMale
      totalPositive
      totalNegative
      ageRanges {
        category
        count
      }
    }
  }
`;

// getHepatitisCTesting
export const GET_HEPATITIS_C_TESTING = gql`
  query GetHepatitisCTesting($filter: CasesAnalyticsFilter) {
    getHepatitisCTesting(filter: $filter) {
      name
      totalFemale
      totalMale
      totalPositive
      totalNegative
      ageRanges {
        category
        count
      }
    }
  }
`;

// getMalariaTesting
export const GET_MALARIA_TESTING = gql`
  query GetMalariaTesting($filter: CasesAnalyticsFilter) {
    getMalariaTesting(filter: $filter) {
      name
      totalFemale
      totalMale
      totalPregnant
      ageRanges {
        category
        count
      }
    }
  }
`;

// getTuberculosisCases
export const GET_TUBERCULOSIS_CASES = gql`
  query GetTuberculosisCases($filter: CasesAnalyticsFilter) {
    getTuberculosisCases(filter: $filter) {
      name
      totalFemale
      totalMale
    }
  }
`;

// getOtherCases
export const GET_OTHER_CASES = gql`
  query GetOtherCases($filter: CasesAnalyticsFilter) {
    getOtherCases(filter: $filter) {
      name
      count
      category
    }
  }
`;

// getCasesOverview
export const GET_CASES_OVERVIEW = gql`
  query GetCasesOverview($filter: CasesAnalyticsFilter) {
    getCasesOverview(filter: $filter) {
      count
      category
      hospitalName
    }
  }
`;

// getDeathsAmongIndividuals
export const GET_DEATHS_AMONG_INDIVIDUALS = gql`
  query GetDeathsAmongIndividuals($filter: DeathsAnalyticsFilter) {
    getDeathsAmongIndividuals(filter: $filter) {
      name
      totalFemale
      totalMale
      ageRanges {
        category
        count
      }
      byCauseOfDeath {
        category
        count
      }
    }
  }
`;

// getMaternalDeaths
export const GET_MATERNAL_DEATHS = gql`
  query GetMaternalDeaths($filter: DeathsAnalyticsFilter) {
    getMaternalDeaths(filter: $filter) {
      name
      ageRanges {
        category
        count
      }
      byCauseOfDeath {
        category
        count
      }
    }
  }
`;

// getNeoNatalDeaths
export const GET_NEO_NATAL_DEATHS = gql`
  query GetNeoNatalDeaths($filter: DeathsAnalyticsFilter) {
    getNeoNatalDeaths(filter: $filter) {
      name
      totalFemale
      totalMale
      byCauseOfDeath {
        category
        count
      }
    }
  }
`;
// getUnderFiveDeaths
export const GET_UNDER_FIVE_DEATHS = gql`
  query GetUnderFiveDeaths($filter: DeathsAnalyticsFilter) {
    getUnderFiveDeaths(filter: $filter) {
      name
      totalFemale
      totalMale
      byCauseOfDeath {
        category
        count
      }
    }
  }
`;

// getDeathsOverview
export const GET_DEATHS_OVERVIEW = gql`
  query GetDeathsOverview($filter: DeathsAnalyticsFilter) {
    getDeathsOverview(filter: $filter) {
      totalDeaths
      totalMaternalDeaths
      totalNeonatalDeaths
      name
      groupedData {
        totalDeaths {
          count
          hospitalId
          hospitalName
        }
        maternalAndNeonatalDeaths {
          hospitalId
          hospitalName
          totalMaternalDeaths
          totalNeonatalDeaths
        }
      }
    }
  }
`;

// getFamilyPlanningOverview
export const GET_FAMILY_PLANNING_OVERVIEW = gql`
  query GetFamilyPlanningOverview($filter: FamilyPlanningAnalyticsFilter) {
    getFamilyPlanningOverview(filter: $filter) {
      count
      category
      hospitalName
    }
  }
`;

// getMaternalOverview
export const GET_MATERNAL_OVERVIEW = gql`
  query GetMaternalOverview($filter: MaternalHealthAnalyticsFilter) {
    getMaternalOverview(filter: $filter) {
      name
      count
      category
      hospitalName
    }
  }
`;

// getAdverseEffectsFollowingImmunizationData
export const GET_ADVERSE_EFFECTS_FOLLOWING_IMMUNIZATION_DATA = gql`
  query GetAdverseEffectsFollowingImmunizationData($filter: ServicesAnalyticsFilter) {
    getAdverseEffectsFollowingImmunizationsData(filter: $filter) {
      totalSeriousAdverseEffects
      totalNonSeriousAdverseEffects
      name
    }
  }
`;

// getDeathsByDepartment
export const GET_DEATHS_BY_DEPARTMENT = gql`
  query GetDeathsByDepartment($filter: DeathsAnalyticsFilter) {
    getDeathsByDepartment(filter: $filter) {
      count
      name
      category
    }
  }
`;

// getInternalAndExternalInvestigation
export const GET_INTERNAL_AND_EXTERNAL_INVESTIGATION = gql`
  query GetInternalAndExternalInvestigation($filter: ServicesAnalyticsFilter) {
    getInternalAndExternalInvestigation(filter: $filter) {
      servicesSummary {
        name
        totalInternalInvestigations
        totalExternalInvestigations
        totalInternalInvestigationsQuantity
        totalExternalInvestigationsQuantity
        totalExternalInvestigationsAmount
        totalInternalInvestigationsAmount
      }
      list {
        byInternalAndExternalInvestigation {
          patientName
          examinationDate
          name
          radiographerName
          totalAmount
          clinifyId
          quantity
          paymentType
          testDate
          performedBy
          isExternal
        }
      }
    }
  }
`;

export const GET_SERVICES_BY_COVERAGE_TYPE = gql`
  query GetServicesByCoverageType($filter: ServicesAnalyticsFilter) {
    getServicesByCoverageType(filter: $filter) {
      list {
        servicesByCoverageType {
          name
          coverageName
          coverageType
          totalCount
          totalAmount
          totalDiscount
          totalAmountDue
          totalAmountPaid
          totalAmountOwing
          totalAmountOutstanding
          patientEmail
          patientName
          visitDate
          memberNumber
          patientPhone
        }
      }
    }
  }
`;

export const GET_NURSING_SERVICE_DATA = gql`
  query GetNursingServiceData($filter: ServicesAnalyticsFilter) {
    getNursingServiceData(filter: $filter) {
      totalMale
      totalFemale
      name
      totalFemaleAmount
      totalMaleAmount
      byAgeRange {
        procedureType
        totalMale
        totalFemale
        ageRange
        totalMaleAmount
        totalFemaleAmount
      }
      byNurseName {
        nurseName
        procedureType
        procedureDate
        amount
        patientName
        clinifyId
        paymentType
        quantity
      }
    }
  }
`;

export const GET_REQUESTED_TO_FACILITY = gql`
  query GetRequestedToFacility($filter: ServicesAnalyticsFilter) {
    getRequestedToFacility(filter: $filter) {
      name
      total
      facilityName
      requestType
    }
  }
`;

export const GET_MEDICAL_REPORT_DATA = gql`
  query GetMedicalReportData($filter: ServicesAnalyticsFilter) {
    getMedicalReportData(filter: $filter) {
      name
      totalMale
      totalFemale
      totalOther
      totalOtherAmount
      totalMaleAmount
      totalFemaleAmount
      byAgeRange {
        medicalReportType
        ageRange
        totalMale
        totalFemale
        totalOther
        totalFemaleAmount
        totalMaleAmount
        totalOtherAmount
      }
      byPatientName {
        reportedBy
        reportDate
        patientName
        paymentType
        totalAmount
        clinifyId
        name
      }
    }
  }
`;

export const GET_BILL_STATUS_SUMMARY = gql`
  query GetBillStatusSummary($filter: FinanceAnalyticsFilter!) {
    getBillStatusDataSummary(filter: $filter) {
      name
      totalCancelled
      totalPaid
      totalPartiallyPaid
      totalPending
    }
  }
`;

export const GET_FINANCE_DATA_SUMMARY = gql`
  query GetFinanceDataSummary($filter: FinanceAnalyticsFilter!) {
    getFinanceDataSymmary(filter: $filter) {
      name
      totalAmountDue
      totalAmount
      totalAmountOutstanding
      totalAmountPaid
      totalDiscountAmount
      totalRevenue
    }
  }
`;

export const GET_SERVICE_REVENUE_DATA_SUMMARY = gql`
  query GetServiceRevenueDataSummary($filter: FinanceAnalyticsFilter!) {
    getServiceRevenueDataSummary(filter: $filter) {
      name
      category
      totalDiscountAmount
      totalAmount
      totalAmountDue
      totalAmountOutstanding
      totalAmountPaid
      totalRevenue
    }
  }
`;

export const GET_REVENUE_BY_DEPARTMENT_SUMMARY = gql`
  query GetRevenueByDepartmentSummary($filter: FinanceAnalyticsFilter!) {
    getRevenueByDepartmentSummary(filter: $filter) {
      totalAmountDue
      totalAmount
      category
      name
      totalAmountOutstanding
      totalAmountPaid
      totalDiscountAmount
      totalRevenue
    }
  }
`;

export const GET_REVENUE_BY_SPECIALTY_SUMMARY = gql`
  query GetRevenueBySpecialtySummary($filter: FinanceAnalyticsFilter!) {
    getRevenueBySpecialtySummary(filter: $filter) {
      totalRevenue
      totalDiscountAmount
      totalAmountPaid
      totalAmount
      totalAmountOutstanding
      name
      category
      totalAmountDue
    }
  }
`;

export const GET_HMO_REVENUE_SUMMARY = gql`
  query GetHMORevenueSummary($filter: FinanceAnalyticsFilter!) {
    getHMORevenueDataSummary(filter: $filter) {
      totalAmountDue
      totalAmount
      category
      name
      totalAmountOutstanding
      totalAmountPaid
      totalDiscountAmount
      totalRevenue
    }
  }
`;

export const GET_COMPANY_REVENUE_SUMMARY = gql`
  query GetCompanyRevenueSummary($filter: FinanceAnalyticsFilter!) {
    getCompanyRevenueDataSummary(filter: $filter) {
      totalDiscountAmount
      totalAmountPaid
      totalAmount
      totalAmountOutstanding
      name
      category
      totalAmountDue
      totalRevenue
    }
  }
`;

export const GET_FAMILY_REVENUE_SUMMARY = gql`
  query GetFamilyRevenueSummary($filter: FinanceAnalyticsFilter!) {
    getFamilyRevenueDataSummary(filter: $filter) {
      totalRevenue
      totalAmountDue
      totalAmount
      category
      name
      totalAmountOutstanding
      totalAmountPaid
      totalDiscountAmount
    }
  }
`;

export const GET_PRIVATE_REVENUE_SUMMARY = gql`
  query GetPrivateRevenueSummary($filter: FinanceAnalyticsFilter!) {
    getPrivateRevenueDataSummary(filter: $filter) {
      totalAmountDue
      totalAmount
      category
      name
      totalAmountOutstanding
      totalAmountPaid
      totalDiscountAmount
      totalRevenue
    }
  }
`;

export const GET_REVENUE_BY_PAYMENT_TYPE_SUMMARY = gql`
  query GetRevenueByPaymentTypeSummary($filter: FinanceAnalyticsFilter!) {
    getPaymentTypeDataSummary(filter: $filter) {
      totalAmountDue
      totalAmount
      category
      name
      totalAmountOutstanding
      totalAmountPaid
      totalDiscountAmount
      totalRevenue
    }
  }
`;

export const GET_DIAGNOSIS_SURVEILLANCE = gql`
  query GetDiagnosisSurveillance($filter: DiagnosisSurveillanceInput!, $isHmo: Boolean) {
    getDiagnosisSurveillance(filter: $filter, isHmo: $isHmo) {
      count
      diagnosis
      lga
      state
    }
  }
`;

export const GET_FINANCE_REPORT = gql`
  query GetHmoFinanceData($filter: HmosAnalyticsFilter!) {
    getHmoFinanceData(filter: $filter) {
      code
      batchStatus
      claimStatus
      providerName
      providerCode
      providerType
      providerRegion
      providerSubregion
      payerName
      employerName
      claimId
      enrolleeName
      programPackage
      programCover
      membershipNumber
      memberNumber
      dateOfBirth
      enrolleeAge
      gender
      treatmentDate
      claimFinalizedDate
      claimFinalizedBy
      itemDescription
      tariffAmount
      phoneNumber
      treatmentQuantity
      totalTreatmentAmount
      diagnosis
      visitDate
      doctorNote
      rejectionReason
      overridingComments
      operator
    }
  }
`;

export const GET_MEDICATION_REPORT = gql`
  query GetHmoMedicationData($filter: HmosAnalyticsFilter!) {
    getHmoMedicationData(filter: $filter) {
      prescribingHospital
      dispensingPharmacy
      enrolleeID
      enrolleeName
      datePrescribed
      medicationName
      medicationCategory
      medicationQuantity
      unitPrice
      totalAmount
      dosage
      dosageUnit
      frequency
      duration
      prescriptionNote
      dispenseDate
      dispenseTime
      status
    }
  }
`;

export const GET_OPERATIONS_REPORT = gql`
  query GetHmoOperationData($filter: HmosAnalyticsFilter!) {
    getHmoOperationData(filter: $filter) {
      totalProvidersSubmittedClaims
      totalNumberOfClaims
      totalSubmittedClaimsAmount
      totalPaidClaimsAmount
      providerReports {
        providerName
        numberOfClaims
        totalSubmittedAmount
        totalPaidAmount
      }
      topProceduresByCost {
        procedureName
        numberOfClaims
        totalSubmittedAmount
      }
      topDiagnosis {
        diagnosis
        numberOfClaims
        totalSubmittedAmount
      }
      topFeeForServiceProviders {
        providerName
        numberOfClaims
        totalSubmittedAmount
        totalPaidAmount
      }
      claimVettedData {
        providerName
        numberOfClaims
        totalSubmittedAmount
        totalPaidAmount
      }
    }
  }
`;

export const GET_ACTUARIAL_REPORT = gql`
  query GetActuarialData($filter: HmosAnalyticsFilter!) {
    getActuarialData(filter: $filter) {
      membershipData {
        enrolleeId
        gender
        planCategory
        planName
        dateOfBirth
        paymentDate
        planStartDate
        planEndDate
        terminationDate
        memberStatus
        enrolleeWard
        providerName
        providerLga
        groupName
        subGroupName
        companyName
        enrolleeLga
        planPremiumFlag
        planPremiumAmount
        paymentFrequency
        premiumCollected
        premiumOutstanding
        beneficiaryDescription
      }
      summaryClaimsData {
        enrolleeId
        providerCode
        providerName
        providerLga
        dateOfBirth
        groupName
        subGroupName
        companyName
        planName
        planCategory
        gender
        treatmentStartDate
        treatmentEndDate
        treatmentType
        claimId
        diagnosisCodes
        diagnosisNames
        amountSubmitted
        providerOwnership
        providerLevel
        admissionDays
        procedureCodes
        drugCodes
        utilizationCategories
        utilizationTypes
        paymentModels
        tariffCharge
        amountCharged
        amountPaid
        paidDate
      }
      proceduresClaimsData {
        enrolleeId
        providerCode
        providerName
        providerLga
        dateOfBirth
        groupName
        subGroupName
        companyName
        planName
        planCategory
        gender
        treatmentStartDate
        treatmentEndDate
        treatmentType
        claimId
        diagnosisCodes
        diagnosisNames
        amountSubmitted
        providerOwnership
        providerLevel
        admissionDays
        procedureNames
        procedureCodes
        drugCodes
        utilizationCategories
        paymentModels
        tariffCharge
        amountCharged
        amountPaid
        paidDate
      }
      drugsClaimsData {
        enrolleeId
        providerCode
        providerName
        providerLga
        dateOfBirth
        groupName
        subGroupName
        companyName
        planName
        planCategory
        gender
        treatmentStartDate
        treatmentEndDate
        treatmentType
        claimId
        diagnosisCodes
        diagnosisNames
        amountSubmitted
        providerOwnership
        providerLevel
        admissionDays
        procedureCodes
        drugNames
        drugCodes
        utilizationCategories
        paymentModels
        tariffCharge
        amountCharged
        amountPaid
        paidDate
      }
      capitationData {
        enrolleeName
        enrolleeId
        providerName
        providerCode
        capitationAmount
      }
    }
  }
`;

export const GET_HMO_UTILIZATION_SUMMARY = gql`
  query GetHmoUtilizationSummary($filter: HmoUtilizationFilter!) {
    getHmoUtilizationSummary(filter: $filter) {
      category
      totalAmount
      count
      types {
        type
        totalAmount
        count
        enrolleeCount
        enrolleeName
        hospitalName
        treatmentStartDate
        enrolleeNumber
        providerName
        status
        totalQuantity
        clinicalDiagnosis
        serviceType
        utilisationStatus {
          status
          vettingGroup
        }
      }
    }
  }
`;
export const GET_TOP_HMO_UTILIZATIONS = gql`
  query GetTopHmoUtilizations($filter: HmoUtilizationFilter!) {
    getTopHmoUtilizations(filter: $filter) {
      category
      totalAmount
      count
      types {
        type
        count
      }
    }
  }
`;

export const GET_TOP_CLAIM_DIAGNOSIS = gql`
  query GetTopClaimDiagnosis($filter: HmoUtilizationFilter!) {
    getTopClaimDiagnosis(filter: $filter) {
      diagnosis
      total
      name
      enrolleeCount
    }
  }
`;

export const GET_PREAUTHORIZATION_REPORT_DATA = gql`
  query GetPreAuthorisationReportData($filter: HmosAnalyticsFilter!) {
    getPreAuthorisationReportData(filter: $filter) {
      claimId
      enrolleeNumber
      enrolleeFirstName
      enrolleeLastName
      enrolleeMiddleName
      enrolleePhoneNumber
      enrolleeGender
      diagnosisCode
      diagnosisName
      providerCode
      providerName
      utilizationCode
      utilizationType
      utilizationCategory
      requestedBy
      paCode
      requestedDate
      visitType
      treatmentDate
      legacyId
      dischargeDate
      batchNumber
      amount
      primaryProviderName
      verifiedBy
      planType
      referralCode
      referralProviderCode
      referralProviderName
      approvedBy
      visitId
      tpa
      turnaroundTime
      paStatus
    }
  }
`;

export const GET_CLAIMS_REPORT_DATA = gql`
  query GetClaimsReportData($filter: HmosAnalyticsFilter!) {
    getClaimsReportData(filter: $filter) {
      claimId
      enrolleeNumber
      treatmentCode
      externalInvoice
      batchStatus
      claimsStatus
      providerName
      providerCode
      providerType
      providerRegion
      providerSubRegion
      payerName
      employerName
      enrolleeName
      programPackage
      programCover
      membershipNumber
      memberDob
      memberGender
      treatmentDate
      claimFinalizedDate
      treatmentStatus
      itemDescription
      tariffAmount
      phoneNumber
      treatmentQuantity
      totalTreatment
      diagnosisCode
      diagnosisDescription
      treatmentType
      visitDate
      invoiceNote
      claimsPaidAmount
      rejectionReason {
        rejectionReason
        specifyReasonForRejection
      }
      overridingComments
      operator
      submissionDate
      encounterMonth
      encounterMonthTo
      lga
      utilisationStatus {
        vettingGroup
      }
    }
  }
`;
