import { gql } from '@apollo/client';
import { CREATED_BY, UPDATED_BY } from 'apollo-queries/fragments/audit';
import { PREAUTHORIZATION_DETAILS_FRAGMENT } from '../fragments/preauthorization-details';

export const PREAUTHORIZATION_UTILISATIONS = gql`
  fragment PreauthUtilisations on PreAuthUtilisationsModel {
    id
    category
    type
    quantity
    price
    status
    paCode
    medicationCategory
    dosage
    dosageUnit
    frequency
    duration
    birthCount
    deliveryDateTime
    specialty
    utilizationCode
    utilizationId
    rejectionReason
    specifyReasonForRejection
    statusDescription
    creatorName
    createdDate
    lastModifierName
    updatedDate
    serviceName
    confirmation
    percentageCovered
    amountCovered
    paymentModel
    flags {
      flag
      ruleId
    }
    utilisationStatus {
      status
      comment
      rejectionReason
      specifyReasonForRejection
      statusDescription
      serviceAmount
      vettingGroup
      creatorId
      creatorName
      createdDate
      updatedDate
    }
    transferFundId
    transferFund {
      id
      amount
      serviceChargeAmount
      transferReference
      destinationBankCode
      destinationBankName
      destinationAccountNumber
      destinationAccountName
      createdDate
      originatorName
      narration
      createdBy {
        id
        fullName
        title
      }
    }
    aiReason {
      reason
      status
    }
  }
`;

export const PREAUTHORIZATIONS = gql`
  fragment Preauthorizations on PreauthorisationModel {
    id
    requestDateTime
    visitId
    claimId
    batchNumber
    requestedBy
    provider {
      id
      name
      providerCode
    }
    serviceType
    serviceTypeCode
    treatmentStartDate
    treatmentEndDate
    serviceName
    priority
    diagnosis {
      diagnosisICD10
      diagnosisICD11
      diagnosisSNOMED
    }
    claimStatus
    additionalNote
    documentUrl
    presentingComplain
    code
    status
    facilityName
    facilityAddress
    rank
    department
    specialty
    isExternalPlanType
    externalPlanTypeId
    utilizations {
      ...PreauthUtilisations
    }
    totalQuantity
    grandTotal
    referredBy
    referralCode
    referredFrom
    referredTo
    profileId
    flags {
      flag
      ruleId
    }
    enrolleeNumber
    enrolleePhoneNumber {
      countryCode
      value
      countryName
    }
    enrolleeEmail
    singleVisitPACode
    profile {
      id
      clinifyId
      fullName
      gender
      secondaryPhoneNumber {
        value
        countryCode
        countryName
      }
      personalInformation {
        id
        dateOfBirth
      }
      user {
        id
        phoneNumber
        nonCorporateEmail
      }
    }
  }
  ${PREAUTHORIZATION_UTILISATIONS}
`;

export const FETCH_PREAUTHORIZATION = gql`
  query Preauthorization($id: String!,$clinifyId: String!) {
    preauthorization(id: $id, clinifyId: $clinifyId) {
      ...Preauthorizations
      enrolleeNumber
      profile {
        id
        clinifyId
        fullName
        gender
        personalInformation {
          id
          dateOfBirth
        }
        user {
          id
          phoneNumber
          nonCorporateEmail
        }
      }
      ${CREATED_BY}
      ${UPDATED_BY}
    }
  }
  ${PREAUTHORIZATIONS}
`;

export const GET_HOSPITAL_PREAUTHORIZATIONS = gql`
  query HospitalPreauthorizations(
    $filterOptions: PreauthorizationFilterInput
    $hospitalId: String
  ) {
    hospital(hospitalId: $hospitalId) {
      id
      preauthorizations(filterOptions: $filterOptions) {
        totalCount
        list {
          ...Preauthorizations
          hospitalId
          utilizations {
            ...PreauthUtilisations
            flags {
              flag
              ruleId
            }
          }
          createdDate
          responseDateTime
          profile {
            id
            clinifyId
            fullName
          }
        }
      }
    }
  }
  ${PREAUTHORIZATION_UTILISATIONS}
  ${PREAUTHORIZATIONS}
`;

export const GET_USER_PREAUTHORIZATIONS = gql`
  query UserPreauthorizations($id: String!, $filterOptions: PreauthorizationFilterInput) {
    profile(clinifyId: $id) {
      id
      preauthorizations(filterOptions: $filterOptions) {
        totalCount
        list {
          ...Preauthorizations
          createdDate
          responseDateTime
        }
      }
    }
  }
  ${PREAUTHORIZATIONS}
`;

export const ADD_PREAUTHORIZATION = gql`
  mutation AddPreauthorization($input: PreauthorisationInput!, $pin: String, $origin: String) {
    addPreauthorization(input: $input, pin: $pin, origin: $origin) {
      ...Preauthorizations
      profile {
        id
        clinifyId
        fullName
      }
      createdDate
      responseDateTime
    }
  }
  ${PREAUTHORIZATIONS}
`;

export const UPDATE_PREAUTHORIZATION = gql`
  mutation UpdatePreauthorization($id: String!, $input: PreauthorisationUpdateInput!, $pin: String) {
    updatePreauthorization(id: $id, input: $input, pin: $pin) {
      ...Preauthorizations
      enrolleeNumber
      profile {
        id
        clinifyId
        fullName
        gender
        personalInformation {
          id
          dateOfBirth
        }
        user {
          id
          phoneNumber
          nonCorporateEmail
        }
      }
      responseDateTime
      ${CREATED_BY}
      ${UPDATED_BY}
    }
  }
  ${PREAUTHORIZATIONS}
`;

export const ARCHIVE_PREAUTHORIZATIONS = gql`
  mutation ArchivePreauthorizations($ids: [String!]!, $archive: Boolean!) {
    archivePreauthorizations(ids: $ids, archive: $archive) {
      ...Preauthorizations
      profile {
        id
        clinifyId
        fullName
      }
      createdDate
      responseDateTime
    }
  }
  ${PREAUTHORIZATIONS}
`;

export const DELETE_PREAUTHORIZATIONS = gql`
  mutation DeletePreauthorizations($ids: [String!]!) {
    deletePreauthorizations(ids: $ids) {
      id
    }
  }
`;

export const GET_AGREED_TARIIF = gql`
  query AgreedTariff($input: AgreedTariffInput!) {
    agreedTariff(input: $input) {
      success
      amount
      position
      label
      utilisationCategory
      utilisationCategoryId
      capitated
    }
  }
`;

export const SYNC_PRE_AUTH = gql`
  mutation SyncPreAuth($ids: [String!]!) {
    synchronizePreauthorizations(ids: $ids) {
      ...Preauthorizations
      createdDate
      responseDateTime
    }
  }
  ${PREAUTHORIZATIONS}
`;

export const MOVE_PREAUTH_TO_CLAIMS = gql`
  mutation MovePreauthToClaims($ids: [String!]!, $origin: String!) {
    movePreauthorizationToClaims(ids: $ids, origin: $origin) {
      ...Preauthorizations
    }
  }
  ${PREAUTHORIZATIONS}
`;

export const UPDATE_PREAUTHORIZATION_DETAILS_SUBS = gql`
  subscription UpdatePreauthorizationDetailsSubs($hospitalId: String!) {
    PreauthorizationDetailsUpdated(hospitalId: $hospitalId) {
      ...PreauthorizationDetails
    }
  }
  ${PREAUTHORIZATION_DETAILS_FRAGMENT}
`;

export const UPDATE_PREAUTHORIZATION_DETAILS = gql`
  mutation UpdatePreauthorizationDetails($input: UpdatePreauthorizationDetailsInput!) {
    updatePreauthorizationDetails(input: $input) {
      ...PreauthorizationDetails
    }
  }
  ${PREAUTHORIZATION_DETAILS_FRAGMENT}
`;

export const PROCESS_PREAUTH_UTILIZATION_STATUS = gql`
  mutation ProcessPreauthUtilizationStatus($input: UpdatePreAuthUtilizationStatusInput!) {
    updatePreAuthUtilizationStatus(input: $input) {
      id
      paCode
      status
      rejectionReason
      specifyReasonForRejection
      statusDescription
      lastModifierName
      updatedDate
      utilisationStatus {
        status
        rejectionReason
        specifyReasonForRejection
        specifyReasonForRejection
        statusDescription
        serviceAmount
        vettingGroup
        creatorId
        creatorName
        createdDate
        updatedDate
        comment
      }
    }
  }
`;

export const PROCESS_BULK_PREAUTH_UTILIZATIONS_STATUS = gql`
  mutation ProcessBulkPreauthUtilizationsStatus($input: UpdateUtilizationsStatusInput!) {
    updateBulkUtilizationsStatus(input: $input) {
      id
      status
      rejectionReason
      statusDescription
      utilisationStatus {
        status
        rejectionReason
        specifyReasonForRejection
        specifyReasonForRejection
        statusDescription
        serviceAmount
        vettingGroup
        creatorId
        creatorName
        createdDate
        updatedDate
        comment
      }
    }
  }
`;

export const BULK_UPDATE_PA_UTILIZATION_STATUS = gql`
  mutation BulkUpdatePAUtilizationStatus($input: UpdateUtilizationsStatusInput!) {
    updatePreAuthUtilizationsStatus(input: $input) {
      id
      utilizations {
        id
        status
        updatedDate
        lastModifierName
      }
    }
  }
`;

export const UTILIZATION_UPDATED_SUBS = gql`
  subscription UtilizationUpdatedSubs(
    $profileId: String!
    $hospitalId: String!
    $hmoProviderId: String
  ) {
    UtilizationUpdated(
      profileId: $profileId
      hospitalId: $hospitalId
      hmoProviderId: $hmoProviderId
    ) {
      ...PreauthUtilisations
    }
  }
  ${PREAUTHORIZATION_UTILISATIONS}
`;

export const GET_UTILIZATION = gql`
  query GetUtilization($paCode: String!, $utilizationId: String!, $utilType: String) {
    getUtilization(paCode: $paCode, utilizationId: $utilizationId, utilType: $utilType) {
      ...PreauthUtilisations
    }
  }
  ${PREAUTHORIZATION_UTILISATIONS}
`;

export const UPDATE_PREAUTH_UTILIZATION_QUANTITY = gql`
  mutation UpdatePreauthUtilizationQuantity($id: String!, $quantity: Float!) {
    updatePreauthUtilizationQuantity(id: $id, quantity: $quantity) {
      id
      quantity
      lastModifierId
      lastModifierName
    }
  }
`;

export const HMOPREAUTHORIZATION_ADDED_SUBS = gql`
  subscription HMOPreauthorizationAddedSubs(
    $profileId: String!
    $hospitalId: String!
    $hmoProviderId: String
  ) {
    HMOPreauthorizationAdded(
      profileId: $profileId
      hospitalId: $hospitalId
      hmoProviderId: $hmoProviderId
    ) {
      ...Preauthorizations
    }
  }
  ${PREAUTHORIZATIONS}
`;

export const HMOPREAUTHORIZATION_UPDATED_SUBS = gql`
  subscription HMOPreauthorizationUpdatedSubs(
    $profileId: String!
    $hospitalId: String!
    $hmoProviderId: String
  ) {
    HMOPreauthorizationUpdated(
      profileId: $profileId
      hospitalId: $hospitalId
      hmoProviderId: $hmoProviderId
    ) {
      ...Preauthorizations
    }
  }
  ${PREAUTHORIZATIONS}
`;

export const FLAG_PREAUTHORIZATION = gql`
  mutation FlagPreauthorization($id: String!, $flag: String!, $unset: Boolean!) {
    flagPreauthorization(id: $id, flag: $flag, unset: $unset) {
      id
      flags {
        flag
        ruleId
      }
      lastModifierId
      lastModifierName
    }
  }
`;

export const PREAUTHORIZATION_FLAGGED_SUBS = gql`
  subscription PreauthorizationFlagged($hospitalId: String!) {
    PreauthorizationFlagged(hospitalId: $hospitalId) {
      id
      flags {
        flag
        ruleId
      }
      lastModifierId
      lastModifierName
    }
  }
`;

export const GET_PROVIDERS_WITH_REQUESTED_PREAUTHORIZATIONS = gql`
  query GetProvidersWithRequestedPreauthorizations($filterOptions: PreauthorizationFilterInput!) {
    getProvidersWithRequestedPreauthorizations(filterOptions: $filterOptions) {
      id
      name
      address
      hospitalSupportPhoneNumber {
        countryCode
        countryName
        value
      }
      supportMail
    }
  }
`;

export const GET_ALL_UTILIZATION_TYPES = gql`
  query GetAllUtilizationTypes($providerId: String!, $visitTypeId: String!, $keyword: String!) {
    getAllUtilizationTypes(keyword: $keyword, providerId: $providerId, visitTypeId: $visitTypeId) {
      label
      value
      code
      hmoPlanBenefitId
      utilizationCategory
    }
  }
`;

export const FIND_UTILIZATION_TYPE = gql`
  query FindUtilizationType($providerId: String!, $visitTypeId: String!, $keyword: String!) {
    findUtilizationType(keyword: $keyword, providerId: $providerId, visitTypeId: $visitTypeId) {
      label
      value
      code
      hmoPlanBenefitId
      utilizationCategory
    }
  }
`;

export const FLAG_UTILIZATIONS = gql`
  mutation FlagUtilizations($utilizationIds: [String!]!, $flag: String!, $unset: Boolean!) {
    flagUtilizations(utilizationIds: $utilizationIds, flag: $flag, unset: $unset) {
      id
      flags {
        flag
      }
      lastModifierId
      lastModifierName
    }
  }
`;

export const UTILIZATION_FLAGGED_SUBS = gql`
  subscription UtilizationFlagged($hospitalId: String!) {
    UtilizationFlagged(hospitalId: $hospitalId) {
      id
      flags {
        flag
        ruleId
      }
      lastModifierId
      lastModifierName
    }
  }
`;

export const GET_PREAUTHORIZATION_SUMMARY = gql`
  query GetPreauthorizationSummary(
    $filterOptions: PreauthorizationFilterInput!
    $profileId: String
  ) {
    getPreauthorizationSummary(filterOptions: $filterOptions, profileId: $profileId) {
      totalPreauthorizations
      totalPendingPreauthorizations
      totalApprovedApprovedPreauthorizations
      totalRejectedPreauthorizations
      totalPreauthorizationsAmount
      totalPendingPreauthorizationsAmount
      totalApprovedApprovedPreauthorizationsAmount
      totalRejectedPreauthorizationsAmount
    }
  }
`;
export const GET_EXTERNAL_PLAN_TYPE = gql`
  query GetExternalPlanType($hmoProviderId: String!) {
    getExternalPlanType(hmoProviderId: $hmoProviderId) {
      id
      name
      isExternalPlan
    }
  }
`;
