import { gql } from '@apollo/client';
import { COVERAGE_DETAILS_FIELDS } from 'apollo-queries/mains/converage';
import { AUDIT_FIELDS } from 'apollo-queries/fragments/audit';
import { PERSONAL_INFORMATION_FIELDS } from './user';
import { WAITING_LIST_FIELDS } from './waitingList';
import {
  CHEMO_VERIFICATION_TEMPLATE_FRAGMENT,
  FACILITY_BILLING_INFORMATION_FRAGMENT,
  FACILITY_BRANCH_FRAGMENT,
  FACILITY_PREFERENCE_FRAGMENT,
  FACILITY_PREFERENCE_MANDATORY_FIELDS_FRAGMENT,
  FACILITY_PREFERENCE_TEST_REFERENCE_RANGE_FRAGMENT,
} from '../fragments/hospital';

export const CAPITATION_TRANSFER_FUND_FIELDS = gql`
  fragment CapitationTransferFundFields on TransferFundModel {
    id
    createdBy {
      id
      fullName
      type
      clinifyId
      title
    }
    amount
    createdDate
    updatedDate
    destinationAccountName
    destinationAccountNumber
    destinationBankCode
    destinationBankName
    narration
    originatorName
    sourceAccountNumber
    transferReference
    transferStatus
    additionalNote
    serviceChargeAmount
    hospitalId
    hmoPlanTypeId
    hospital {
      id
      name
      address
    }
    enrolleeCount
    isEnrolleePayout
    totalCapitationAmount
    auditApproval {
      createdDate
      creatorName
      approvalGroup
      creatorId
    }
    hmoPlanType {
      id
      name
    }
    detailsByPlanType {
      enrolleeCount
      planType
      planTypeName
      totalCapitationAmount
      payoutDecreasePercentage
    }
    payoutCommissionPayer
    payoutDecreasePercentage
  }
`;

export const GET_HOSPITALS = gql`
  query GetHospitals($filterOptions: HospitalFilterInput) {
    hospitals(filterOptions: $filterOptions) {
      totalCount
      list {
        id
        clinifyId
        name
        address
        ownership
        lga
        politicalWard
        city
        state
        level
      }
    }
  }
`;

export const GET_HOSPITALS_WITH_ADMIN = gql`
  query GetHospitalsWithAdmin(
    $filterOptions: HospitalFilterInput
    $startDate: String
    $endDate: String
    $planId: String
  ) {
    hospitals(filterOptions: $filterOptions) {
      totalCount
      list {
        id
        clinifyId
        name
        address
        ownership
        lga
        politicalWard
        city
        state
        level
        supportMail
        country
        classification
        plan
        hospitalSupportPhoneNumber {
          countryCode
          value
          countryName
        }
        preferredPayoutAccount {
          accountNumber
          bankName
          accountName
        }
        capitationDetails(startDate: $startDate, endDate: $endDate, planId: $planId) {
          enrolleeCount
          totalCapitationAmount
          detailsByPlanType {
            enrolleeCount
            planType
            planTypeName
            totalCapitationAmount
            payoutDecreasePercentage
          }
          payoutDecreasePercentage
          transferFund {
            ...CapitationTransferFundFields
          }
        }
        hmoHospitals {
          id
          hmoProviderUniqueId
          hmoProviderId
          tariffBand
          category
          planVisibility {
            id
            name
          }
        }
        phoneNumber {
          countryCode
          value
        }
        secondaryPhoneNumber {
          countryCode
          value
        }
        orgAdmin {
          id
          fullName
          personalInformation {
            firstName
            lastName
          }
          user {
            id
            email
            corporatePhoneNumber
          }
        }
      }
    }
  }
  ${CAPITATION_TRANSFER_FUND_FIELDS}
`;

export const GET_HOSPITAL_SPECIALTIES = gql`
  query GetHospitalSpecialties($hospitalId: String) {
    hospital(hospitalId: $hospitalId) {
      id
      specialties
    }
  }
`;

export const GET_HOSPITAL_ROLES = gql`
  query GetHospitalRoles($hospitalId: String) {
    hospital(hospitalId: $hospitalId) {
      id
      roles
    }
  }
`;

export const HOSPITAL_WAITING_LIST = gql`
  query GetHospitalWaitingList($filterOptions: WaitingListFilterInput!, $hospitalId: String) {
    hospital(hospitalId: $hospitalId) {
      id
      waitingList(filterOptions: $filterOptions) {
        totalCount
        list {
          ...Waiter
        }
      }
    }
  }
  ${WAITING_LIST_FIELDS}
`;

export const GET_HOSPITAL_STAFFS = gql`
  query GetHospitalStaffs($filterOptions: StaffsFilterInputs, $hospitalId: String) {
    hospital(hospitalId: $hospitalId) {
      id
      name
      staffs(filterOptions: $filterOptions) {
        totalCount
        list {
          id
          fullName
          type
          typeAlias
          title
          clinifyId
          active
          personalInformation {
            ...PersonalInformationFields
          }
        }
      }
    }
  }
  ${PERSONAL_INFORMATION_FIELDS}
`;

export const GET_HOSPITAL_STAFFS_FOR_VISITATION = gql`
  query GetHospitalStaffsForVisitation($filterOptions: StaffsFilterInputs, $hospitalId: String) {
    hospital(hospitalId: $hospitalId) {
      id
      staffs(filterOptions: $filterOptions) {
        totalCount
        list {
          id
          fullName
          type
          title
          clinifyId
          active
          personalInformation {
            id
            rank
            title
            department
            dateOfBirth
            height
            gender
            speciality
          }
        }
      }
    }
  }
`;

export const GET_HOSPITAL_STAFF_WITH_WAITER_COUNT = gql`
  query GetHospitalStaffWithWaiterCount($filterOptions: StaffsFilterInputs, $hospitalId: String) {
    hospital(hospitalId: $hospitalId) {
      id
      staffs(filterOptions: $filterOptions) {
        totalCount
        list {
          id
          fullName
          type
          title
          waiterCount
          personalInformation {
            ...PersonalInformationFields
          }
        }
      }
    }
  }
  ${PERSONAL_INFORMATION_FIELDS}
`;

export const GET_HOSPITAL_STAFFS_MIN = gql`
  query GetHospitialStaffsMin($filterOptions: StaffsFilterInputs, $hospitalId: String) {
    hospital(hospitalId: $hospitalId) {
      id
      name
      clinifyId
      staffs(filterOptions: $filterOptions) {
        totalCount
        list {
          id
          fullName
          title
          clinifyId
          personalInformation {
            rank
            speciality
            department
          }
          user {
            id
            isPinMandatory
            hasPin
          }
        }
      }
    }
  }
`;

export const GET_HOSPITAL_APPOINTMENTS = gql`
  query GetHospitalAppointments(
    $filterOptions: OrganisationAppointmentFilterInput
    $hospitalId: String
    $specialistId: String
  ) {
    hospital(hospitalId: $hospitalId) {
      id
      name
      address
      ownership
      lga
      politicalWard
      level
      state
      appointments(filterOptions: $filterOptions, specialistId: $specialistId) {
        totalCount
        list {
          id
          profile {
            id
            fullName
            clinifyId
            coverageDetails {
              id
              name
              coverageType
              companyName
              familyName
              memberNumber
              provider {
                id
                name
              }
            }
          }
          specialist {
            id
            clinifyId
            type
            fullName
            personalInformation {
              ...PersonalInformationFields
            }
          }
          hospital {
            id
            name
          }
          patientInformation {
            phone
            email
            fullName
            clinifyId
          }
          status
          createdDate
          updatedDate
          category
          rank
          reason
          appointmentDateTime
          startDateTime
          endDateTime
          specialty
          paymentType
          patientType
          duration
          confirmedBy
          deliveryMethod
          urgency
          creatorName
          patientConfirmation
          patientWhatsappConfirmation
          patientSmsConfirmation
          serviceDetails {
            type
            name
          }
        }
      }
    }
  }
  ${PERSONAL_INFORMATION_FIELDS}
`;

export const REGISTER_PATIENT = gql`
  mutation RegisterPatient($input: PatientRegistrationInput!) {
    registerPatient(registrationInput: $input) {
      id
      clinifyId
      fullName
      type
      coverageDetails {
        ...CoverageDetailsField
      }
      shareData
      dataAccessType
      fileNumbers {
        coverageRef
        fileNumber
        existingFamily
      }
      serviceDetails {
        priceId
        type
        name
        quantity
        pricePerUnit
        patientType
        paymentType
      }
      registeredWithId
      personalInformation {
        ...PersonalInformationFields
      }
      user {
        id
        email
        nonCorporateEmail
        phoneNumber
        country
      }
    }
  }
  ${PERSONAL_INFORMATION_FIELDS}
  ${COVERAGE_DETAILS_FIELDS}
`;

export const DELETE_REGISTERED_PATIENTS = gql`
  mutation DeletePatientRegisteredWithHospital($ids: [String!]!) {
    deletePatientRegisteredWithHospital(profileIds: $ids) {
      id
    }
  }
`;

export const ARCHIVE_REGISTERED_PATIENTS = gql`
  mutation ArchivePatientRegisteredWithHospital($ids: [String!]!, $archive: Boolean) {
    archivePatientRegisteredWithHospital(ids: $ids, archive: $archive) {
      id
      clinifyId
      fullName
      type
      personalInformation {
        ...PersonalInformationFields
      }
      user {
        id
        email
        nonCorporateEmail
        phoneNumber
        country
      }
    }
  }
  ${PERSONAL_INFORMATION_FIELDS}
`;

export const TRANSFER_EVENT_SUBSCRIPTION = gql`
  subscription TransferEvent($hospitalId: String!) {
    TransferEvent(hospitalId: $hospitalId)
  }
`;

export const CAN_CREATE_HOSPITAL = gql`
  query CanCreateHospital($password: String!) {
    verifyCreationPassword(password: $password)
  }
`;

// Mutate Services & Providers
export const PROVIDER_PAYLOAD = gql`
  fragment Providers on HospitalProviders {
    id
    name
    code
    createdOn
    creatorName
  }
`;

export const SERVICE_PAYLOAD = gql`
  fragment Services on HospitalService {
    id
    name
    createdOn
    updatedOn
    description
    creatorName
  }
`;

const INVENTORY_ITEM_PAYLOAD = gql`
  fragment InventoryItems on HospitalInventoryItem {
    id
    name
    description
    createdOn
    creatorName
  }
`;

export const GET_HOSPITAL_PROPERTIES = gql`
  query GetHospital($hospitalId: String) {
    hospital(hospitalId: $hospitalId) {
      id
      inventoryItems {
        ...InventoryItems
      }
      providers {
        ...Providers
      }
      services {
        ...Services
      }
    }
  }
  ${INVENTORY_ITEM_PAYLOAD}
  ${SERVICE_PAYLOAD}
  ${PROVIDER_PAYLOAD}
`;

export const GET_HOSPITAL_INFO = gql`
  query GetHospitalInfo($hospitalId: String!) {
    hospital(hospitalId: $hospitalId) {
      id
      name
      clinifyId
      facilityLogo
    }
  }
`;

export const ADD_HOSPITAL_PROVIDERS = gql`
  mutation addHospitalProviders($providers: [HospitalProviderInput!]!, $csvImport: Boolean) {
    addHospitalProviders(provider: $providers, csvImport: $csvImport) {
      id
      providers {
        ...Providers
      }
    }
  }
  ${PROVIDER_PAYLOAD}
`;

export const REMOVE_HOSPITAL_PROVIDERS = gql`
  mutation removeHospitalProviders($ids: [String!]!) {
    removeHospitalProviders(ids: $ids) {
      id
      providers {
        ...Providers
      }
    }
  }
  ${PROVIDER_PAYLOAD}
`;

export const UPDATE_HOSPITAL_PROVIDERS = gql`
  mutation updateHospitalProviders($provider: HospitalProviderInput!, $id: String!) {
    updateHospitalProvider(id: $id, provider: $provider) {
      id
      providers {
        ...Providers
      }
    }
  }
  ${PROVIDER_PAYLOAD}
`;

export const ADD_HOSPITAL_SERVICES = gql`
  mutation AddHospitalServices($services: [HospitalServiceInput!]!, $csvImport: Boolean) {
    addHospitalServices(services: $services, csvImport: $csvImport) {
      id
      services {
        ...Services
      }
    }
  }
  ${SERVICE_PAYLOAD}
`;

export const REMOVE_HOSPITAL_SERVICES = gql`
  mutation removeHospitalServices($ids: [String!]!) {
    removeHospitalServices(ids: $ids) {
      id
      services {
        ...Services
      }
    }
  }
  ${SERVICE_PAYLOAD}
`;

export const UPDATE_HOSPITAL_SERVICE = gql`
  mutation updateHospitalService($service: HospitalServiceInput!, $id: String!) {
    updateHospitalService(id: $id, service: $service) {
      id
      services {
        ...Services
      }
    }
  }
  ${SERVICE_PAYLOAD}
`;

export const ADD_HOSPITAL_INVENTORY_ITEM = gql`
  mutation addHospitalInventoryItem($inventoryItems: [HospitalInventoryItemInput!]!) {
    addHospitalInventoryItem(inventoryItems: $inventoryItems) {
      id
      inventoryItems {
        ...InventoryItems
      }
    }
  }
  ${INVENTORY_ITEM_PAYLOAD}
`;

export const REMOVE_HOSPITAL_INVENTORY_ITEM = gql`
  mutation removeHospitalInventoryItems($ids: [String!]!) {
    removeHospitalInventoryItems(ids: $ids) {
      id
      inventoryItems {
        ...InventoryItems
      }
    }
  }
  ${INVENTORY_ITEM_PAYLOAD}
`;

export const UPDATE_HOSPITAL_INVENTORY_ITEM = gql`
  mutation updateHospitalInventoryItem($inventoryItem: HospitalInventoryItemInput!, $id: String!) {
    updateHospitalInventoryItem(id: $id, inventoryItem: $inventoryItem) {
      id
      inventoryItems {
        ...InventoryItems
      }
    }
  }
  ${INVENTORY_ITEM_PAYLOAD}
`;

export const INVENTORY_ITEM_UPDATED_SUBS = gql`
  subscription InventoryItemUpdatedSubs($hospitalId: String!) {
    InventoryItemUpdated(hospitalId: $hospitalId) {
      id
      inventoryItems {
        ...InventoryItems
      }
    }
  }
  ${INVENTORY_ITEM_PAYLOAD}
`;

export const ADD_FACILITY_BILLING_INFORMATION = gql`
  mutation AddFacilityBillingInformation(
    $hospitalId: String!
    $input: FacilityBillingInformationInput!
  ) {
    addFacilityBillingInformation(hospitalId: $hospitalId, input: $input) {
      ...FacilityBillingInformation
    }
  }
  ${FACILITY_BILLING_INFORMATION_FRAGMENT}
`;

export const UPDATE_FACILITY_BILLING_INFORMATION = gql`
  mutation UpdateFacilityBillingInformation(
    $id: String!
    $input: FacilityBillingInformationInput!
  ) {
    updateFacilityBillingInformation(billingInformationId: $id, input: $input) {
      ...FacilityBillingInformation
    }
  }
  ${FACILITY_BILLING_INFORMATION_FRAGMENT}
`;

export const UPDATE_FACILITY_BILLING_INFORMATION_BY_AGENCY = gql`
  mutation updateFacilityBillingInformationByAgency(
    $hospitalId: String!
    $input: FacilityBillingInformationInput!
  ) {
    updateFacilityBillingInformationByAgency(hospitalId: $hospitalId, input: $input) {
      ...FacilityBillingInformation
    }
  }
  ${FACILITY_BILLING_INFORMATION_FRAGMENT}
`;

export const SET_PREFERRED_PAYOUT_ACCOUNT = gql`
  mutation SetPreferredPayoutAccount($id: String!) {
    setPreferredPayoutAccount(billingInformationId: $id) {
      ...FacilityBillingInformation
    }
  }
  ${FACILITY_BILLING_INFORMATION_FRAGMENT}
`;

export const DELETE_FACILITY_BILLING_INFORMATION = gql`
  mutation DeleteFacilityBillingInformation($id: String!) {
    deleteFacilityBillingInformation(billingInformationId: $id) {
      id
    }
  }
`;

export const UPDATE_FACILITY_LOGO = gql`
  mutation UpdateFacilityLogo($input: String) {
    updateFacilityLogo(input: $input) {
      id
      facilityLogo
    }
  }
`;

export const EXTRACT_COVERAGE_INFORMATION = gql`
  query ExtractCoverageInformation($enrolleeId: String!, $providerId: String!) {
    extractCoverageInformation(enrolleeId: $enrolleeId, providerId: $providerId) {
      memberNumber
      memberPlan
      employeeNumber
      employeeType
      employeeDivision
      memberStartDate
      memberDueDate
      memberStatus
      companyName
      companyAddress
      primaryProviderName
      secondaryProviderName
      tertiaryProviderName
      primaryProviderAddress
      secondaryProviderAddress
      tertiaryProviderAddress
      capitatedMember
      retired
      occupation
      capitatedAmount

      provider {
        id
        name
      }
    }
  }
`;

export const GET_FACILITY_PREFERENCE = gql`
  query GetFacilityPreference($hospitalId: String!) {
    getFacilityPreference(hospitalId: $hospitalId) {
      ...FacilityPreference
    }
  }
  ${FACILITY_PREFERENCE_FRAGMENT}
`;

export const UPDATE_WELCOME_MAIL_TEMPLATE = gql`
  mutation UpdateWelcomeMailTemplate($hospitalId: String!, $input: MailTemplateInput) {
    updateWelcomeMailTemplate(hospitalId: $hospitalId, input: $input) {
      id
      welcomeMailTemplate {
        subject
        body
      }
      updatedDate
      lastModifierName
    }
  }
`;
export const UPDATE_PATIENT_SURVEY_LINKS = gql`
  mutation UpdatePatientSurveyLinks(
    $facilityPreferenceId: String!
    $outPatientLink: String
    $inPatientLink: String
  ) {
    updatePatientSurveyLinks(
      facilityPreferenceId: $facilityPreferenceId
      outPatientLink: $outPatientLink
      inPatientLink: $inPatientLink
    ) {
      id
      outPatientLink
      inPatientLink
    }
  }
`;

export const GET_CUSTOM_MANDATORY_FIELDS = gql`
  query GetCustomMandatoryFields($hospitalId: String!) {
    getFacilityPreference(hospitalId: $hospitalId) {
      ...FacilityPreferenceMandatoryFields
    }
  }
  ${FACILITY_PREFERENCE_MANDATORY_FIELDS_FRAGMENT}
`;

export const UPDATE_CUSTOM_MANDATORY_FIELDS = gql`
  mutation UpdateCustomMandatoryFields($hospitalId: String!, $input: MandatoryFieldsInput) {
    updateFormsMandatoryFields(hospitalId: $hospitalId, input: $input) {
      ...FacilityPreferenceMandatoryFields
    }
  }
  ${FACILITY_PREFERENCE_MANDATORY_FIELDS_FRAGMENT}
`;

export const FETCH_FINDINGS_TEMPLATES = gql`
  query FetchFindingsTemplates($facilityPreferenceId: String) {
    fetchFindingsTemplates(facilityPreferenceId: $facilityPreferenceId) {
      id
      name
      findings
      impression
    }
  }
`;

export const SAVE_FINDINGS_TEMPLATE = gql`
  mutation SaveFindingsTemplate($input: NewFindingsTemplateInput) {
    saveFindingsTemplate(input: $input) {
      id
      name
      findings
      impression
    }
  }
`;

export const UPDATE_FINDINGS_TEMPLATE = gql`
  mutation UpdateFindingsTemplate($input: FindingsTemplateInput) {
    updateFindingsTemplate(input: $input) {
      id
      name
      findings
      impression
    }
  }
`;

export const DELETE_FINDINGS_TEMPLATE = gql`
  mutation DeleteFindingsTemplate($id: String!) {
    deleteFindingsTemplate(id: $id) {
      id
    }
  }
`;

export const FETCH_BY_FACILITY_PREFERENCE_ID_CONSULTATIONS_TEMPLATES = gql`
  query FetchByFacilityPreferenceIdConsultationsTemplates($facilityPreferenceId: String!) {
    findByFacilityPreferenceIdConsultationsTemplates(facilityPreferenceId: $facilityPreferenceId) {
      id
      name
      complaints
      historyComplaints
      healthEducation
      reviewSystems
      physicalExamination
      audiometry
      treatmentPlan
    }
  }
`;

export const FETCH_CONSULTATIONS_TEMPLATES = gql`
  query FetchConsultationsTemplates($facilityPreferenceId: String!) {
    findByHospitalIdConsultationsTemplates(hospitalId: $facilityPreferenceId) {
      id
      name
      complaints
      historyComplaints
      healthEducation
      reviewSystems
      physicalExamination
      audiometry
      treatmentPlan
    }
  }
`;

export const SAVE_CONSULTATIONS_TEMPLATE = gql`
  mutation SaveConsultationsTemplate($input: NewConsultationsTemplateInput!) {
    saveConsultationsTemplate(input: $input) {
      id
      name
      complaints
      historyComplaints
      healthEducation
      reviewSystems
      physicalExamination
      audiometry
      treatmentPlan
    }
  }
`;

export const UPDATE_CONSULTATIONS_TEMPLATE = gql`
  mutation UpdateConsultationsTemplate($input: ConsultationsTemplateInput!) {
    updateConsultationsTemplate(input: $input) {
      id
      name
      complaints
      historyComplaints
      healthEducation
      reviewSystems
      physicalExamination
      audiometry
      treatmentPlan
    }
  }
`;

export const DELETE_CONSULTATIONS_TEMPLATE = gql`
  mutation DeleteConsultationsTemplate($id: String!) {
    deleteConsultationsTemplate(id: $id) {
      id
    }
  }
`;
export const FACILITY_PREFERENCE_SUBS = gql`
  subscription FacilityPreferenceSubs($hospitalId: String!) {
    FacilityPreferenceUpdated(hospitalId: $hospitalId) {
      ...FacilityPreference
    }
  }
  ${FACILITY_PREFERENCE_FRAGMENT}
`;

export const GET_TEST_REFERENCE_RANGE_FIELDS = gql`
  query GetTestReferenceRangeFields($facilityPreferenceId: String!, $testNames: [String!]) {
    fetchTestReferenceRanges(facilityPreferenceId: $facilityPreferenceId, testNames: $testNames) {
      ...TestReferenceRangeFields
    }
  }
  ${FACILITY_PREFERENCE_TEST_REFERENCE_RANGE_FRAGMENT}
`;

export const UPDATE_TEST_REFERENCE_RANGE_FIELDS = gql`
  mutation UpdateTestReferenceRangeFields($input: TestReferenceRangeInput!) {
    updateTestReferenceRange(input: $input) {
      ...TestReferenceRangeFields
    }
  }
  ${FACILITY_PREFERENCE_TEST_REFERENCE_RANGE_FRAGMENT}
`;

export const UPDATE_RECEIPT_SIZE = gql`
  mutation UpdateReceiptSize($receiptSize: String!, $facilityPreferenceId: String!) {
    updateReceiptSize(receiptSize: $receiptSize, facilityPreferenceId: $facilityPreferenceId) {
      id
      receiptSize
    }
  }
`;

export const GET_STAFF_ACTIVITIES_LIST = gql`
  query GetStaffActivitiesSummaryList($filter: StaffActivitiesSummeryFilter!) {
    getStaffActivitiesSummery(filter: $filter) {
      totalCount
      list {
        id
        patientId
        serviceIds
        clinifyId
        fullName
        age
        gender
        serviceTypes
        serviceDates
        billIds
        billStatuses
      }
    }
  }
`;

export const GET_STAFF_ACTIVITIES = gql`
  query GetStaffActivities($filter: StaffActivitiesFilter!) {
    getStaffActivities(filter: $filter) {
      staff {
        fullName
        clinifyId
        type
      }
      list {
        startDate
        endDate
        serviceType
        staff {
          fullName
          clinifyId
          type
        }
        patient {
          fullName
          clinifyId
          weight
          height
          dob
          bloodGroup
          genoType
          gender
          bmi
          coverageType
          coverageName
        }
        investigationRadiology {
          procedureType
          exams {
            name
            radiographerName
            status
            date
            indication
            findings
            impression
          }
          isProcessed
          orderedBy
          date
        }
        investigationLaboratory {
          tests {
            name
            date
            results
            referenceRange
            status
            verifiedBy
          }
          isProcessed
          orderedBy
          date
        }
        vitalSigns {
          id
          bmi
          bsa
          takenBy
          date
          height
          weight
          pulseRate
          heartRate
          bloodPressure
          bloodGlucose
          temperature
          respiratoryRate
          oxygenSaturation
          isHeightCritical
          isWeightCritical
          isOxygenSaturationCritical
          isRespiratoryRateCritical
          isDiastolicCritical
          isSystolicCritical
          isTemperatureCritical
          isPulseRateCritical
          isBloodGlucoseCritical
          isHeartRateCritical
        }
        consultations {
          id
          initialDiagnosis
          finalDiagnosis
          treatmentPlans
          presentingComplaint
          historyOfPresentingComplaint
          doctorName
          reviewOfSystem
          physicalExamination
          chemoNote
          date
          consultationType
          chemoDrugs {
            section
            combinationName
            cycleNumber
            day
            drugName
            dosage
            dosagePercentage
            totalDose
            adjustedDose
            route
            frequency
            chemoDiagnosis
            infusionUsed
            quantity
            administrationDateTime
            administeredBy
            investigations {
              name
              type
            }
          }
        }
        nursingServices {
          procedureType
          dob
          consentGiven
          parentGuardianPresent
          anaesthesiaGiven
          vitaminKGiven
          assistantNurseName
          castLocation
          isItARepeatedCasting
          reasonForCasting
          hasRadiologicalInvestigationBeenDone
          whichEar
          observation
          councelled
          method
          informedConsent
          woundLocation
          dressingType
          dressingAppearance
          dressingIntervention
          lastDressingChange
          painScore
          painDescriptors
          dressingChangeDue
          signOfInfection
          nurseName
          date
        }
        pregnancyCare {
          type
          lastMenstrualPeriod
          estimatedDateOfDelivery
          gravidity
          parity
          maternalBloodPressure
          fetalHeartRate
          symphiosioFundalHeight
          presentation
          position
          lie
          fetalMovement
          oedema
          gestationalAge
          induction
          inductionMethod
          epiduralGiven
          preterm
          fetalMonitoring
          methodOfDelivery
          babyDeliveryDate
          motherStatus
          babyStatus
          babyGender
          birthWeight
          apgarScore
          estimatedBloodLoss
          cervicalTear
          perinealLaceration
          birthInjury
          deliveryDate
          hasMenstruationStarted
          uterus
          breastfeedingChoice
          babyComplaints
          visitationNote
          seenBy
          date
          deliveredBy
        }
        admissions {
          admissionDiagnosis
          admissionWard
          doctorNotes
          nurseNotes
          admissionDate
          admittedBy
          discharges {
            dischargeDiagnosis
            dischargedStatus
            dischargedBy
            deathDate
            dischargeSummary
            dischargedDate
          }
        }
        immunizations {
          routeOfAdministration
          dosage
          quantity
          vacinationName
          givenBy
          date
        }
        medications {
          prescriptions {
            name
            frequency
            routeOfAdministration
            date
            dosage
            quantity
            duration
            prescribedBy
          }
          dispenses {
            name
            frequency
            routeOfAdministration
            date
            dosage
            duration
            quantity
            dispensedBy
          }
          prescribedBy
          date
        }
        consumables {
          consumables {
            name
            quantity
            date
            quantity
            prescribedBy
          }
          dispenses {
            name
            date
            dispensedBy
            quantity
          }
          prescribedBy
          date
        }
        procedures {
          procedureTypes
          postOperationNotes
          endDate
          startDate
          operationNotes
          indication
          patientConsent
          surgeonName
          requestedBy
          date
        }
        medicalReports {
          reportDate
          reportedBy
          report
          reportType
        }
      }
    }
  }
`;

export const UPDATE_PATIENT_ACCESS_TYPE = gql`
  mutation UpdatePatientLookupMode($mode: DefaultPatientAccessType!) {
    updatePatientAccessType(mode: $mode) {
      id
      patientAccessType
      hospitalId
    }
  }
`;

export const FETCH_LAB_COMMENTS_TEMPLATES = gql`
  query FetchLabCommentsTemplates($facilityPreferenceId: String) {
    fetchLabCommentsTemplates(facilityPreferenceId: $facilityPreferenceId) {
      id
      name
      comment
    }
  }
`;

export const SAVE_LAB_COMMENTS_TEMPLATE = gql`
  mutation SaveLabCommentsTemplate($input: NewLabCommentsTemplateInput) {
    saveLabCommentsTemplate(input: $input) {
      id
      name
      comment
    }
  }
`;

export const UPDATE_LAB_COMMENTS_TEMPLATE = gql`
  mutation UpdateLabCommentsTemplate($input: LabCommentsTemplateInput) {
    updateLabCommentsTemplate(input: $input) {
      id
      name
      comment
    }
  }
`;

export const DELETE_LAB_COMMENTS_TEMPLATE = gql`
  mutation DeleteLabCommentsTemplate($id: String!) {
    deleteLabCommentsTemplate(id: $id) {
      id
    }
  }
`;

export const FETCH_MEDICAL_REPORT_TEMPLATES = gql`
  query FetchMedicalReportTemplates($facilityPreferenceId: String) {
    fetchMedicalReportTemplates(facilityPreferenceId: $facilityPreferenceId) {
      id
      template
      name
    }
  }
`;

export const SAVE_MEDICAL_REPORT_TEMPLATE = gql`
  mutation SaveMedicalReportTemplate($input: NewMedicalReportTemplateInput) {
    saveMedicalReportTemplate(input: $input) {
      id
      template
      name
    }
  }
`;

export const UPDATE_MEDICAL_REPORT_TEMPLATE = gql`
  mutation UpdateMedicalReportTemplate($input: MedicalReportTemplateInput) {
    updateMedicalReportTemplate(input: $input) {
      id
      template
      name
    }
  }
`;

export const DELETE_MEDICAL_REPORT_TEMPLATE = gql`
  mutation DeleteMedicalReportTemplate($id: String!) {
    deleteMedicalReportTemplate(id: $id) {
      id
    }
  }
`;

export const UPDATE_RADIOLOGY_CONTRAST_MODE = gql`
  mutation UpdateRadiologyContrastMode($mode: Boolean!) {
    updateRadiologyContrastMode(mode: $mode) {
      id
      radiologyContrastConfirmation
      hospitalId
    }
  }
`;

export const UPDATE_DASHBOARD_COLOUR_MODE = gql`
  mutation UpdateDashboardColourMode($mode: Boolean!) {
    updateDashboardColourMode(mode: $mode) {
      id
      dashboardColourMode
      hospitalId
    }
  }
`;

export const UPDATE_SHOW_SERVICE_DETAILS = gql`
  mutation UpdateShowServiceDetails($mode: Boolean!) {
    updateShowServiceDetails(mode: $mode) {
      id
      showServiceDetails
      rolesServiceDetailsIsHidden
      inventoryClass
      hospitalId
    }
  }
`;

export const UPDATE_INVENTORY_CLASS = gql`
  mutation UpdateInventoryClass($inventoryClass: String!) {
    updateInventoryClass(inventoryClass: $inventoryClass) {
      id
      showServiceDetails
      rolesServiceDetailsIsHidden
      inventoryClass
      hospitalId
    }
  }
`;

export const UPDATE_ROLES_SERVICE_DETAILS_IS_HIDDEN = gql`
  mutation UpdateRolesServiceDetailsIsHidden($roles: [String!]!) {
    updateRolesServiceDetailsIsHidden(roles: $roles) {
      id
      showServiceDetails
      rolesServiceDetailsIsHidden
      inventoryClass
      hospitalId
    }
  }
`;

export const UPDATE_DEFAULT_COMMISSION_PAYER = gql`
  mutation UpdateDefaultCommissionPayer(
    $facilityPreferenceId: String!
    $commissionPayer: CommissionPayer!
  ) {
    updateDefaultCommissionPayer(
      facilityPreferenceId: $facilityPreferenceId
      commissionPayer: $commissionPayer
    ) {
      commissionPayer
      facilityPreferenceId
      hospitalId
    }
  }
`;

export const UPDATE_FILE_NUMBER_GENERATE_MODE = gql`
  mutation UpdateFileNumberGenerate($mode: Boolean!) {
    updateFileNumberGenerate(mode: $mode) {
      id
      generateFileNumber
      hospitalId
    }
  }
`;

export const SAVE_DISCHARGE_SUMMARY_TEMPLATE = gql`
  mutation SaveDischargeSummaryTemplate($input: NewDischargeSummaryTemplateInput!) {
    saveDischargeSummaryTemplate(input: $input) {
      id
      name
      summary
    }
  }
`;

export const UPDATE_DISCHARGE_SUMMARY_TEMPLATE = gql`
  mutation UpdateDischargeSummaryTemplate($input: DischargeSummaryTemplateInput!) {
    updateDischargeSummaryTemplate(input: $input) {
      id
      name
      summary
    }
  }
`;

export const DELETE_DISCHARGE_SUMMARY_TEMPLATE = gql`
  mutation DeleteDischargeSummaryTemplate($id: String!) {
    deleteDischargeSummaryTemplate(id: $id) {
      id
    }
  }
`;

export const FETCH_DISCHARGE_SUMMARY_TEMPLATES = gql`
  query FetchDischargeSummaryTemplates($facilityPreferenceId: String) {
    fetchDischargeSummaryTemplates(facilityPreferenceId: $facilityPreferenceId) {
      id
      name
      summary
    }
  }
`;

export const UPDATE_HMO_SINGLE_VISIT_PA_CODE = gql`
  mutation UpdateHmoSingleVisitPACode($mode: Boolean!) {
    updateHmoSingleVisitPACode(mode: $mode) {
      id
      hmoSingleVisitPACode
    }
  }
`;

export const UPDATE_CUSTOM_PA_FORMAT_TYPE = gql`
  mutation UpdateCustomPaFormatType($mode: Boolean!) {
    updateCustomPaFormatType(mode: $mode) {
      id
      customPaFormatType
    }
  }
`;
export const UPDATE_ENABLE_BUSINESS_RULE_PREVENT_SUBMIT = gql`
  mutation UpdateEnableBusinessRulePreventSubmit($mode: Boolean!) {
    updateEnableBusinessRulePreventSubmit(mode: $mode) {
      id
      enableBusinessRulePreventSubmit
    }
  }
`;
export const GET_CHEMO_VERIFICATION_TEMPLATES = gql`
  query GetChemoVerificationTemplates(
    $facilityPreferenceId: String
    $section: String
    $chemoDiagnosis: String
  ) {
    fetchChemoDiagnosisTemplates(
      facilityPreferenceId: $facilityPreferenceId
      section: $section
      chemoDiagnosis: $chemoDiagnosis
    ) {
      id
      combinationName
      type
    }
  }
`;

export const GET_CHEMO_VERIFICATION_TEMPLATE = gql`
  query GetChemoVerificationTemplate($id: String!) {
    fetchChemoDiagnosisTemplate(id: $id) {
      ...ChemoVerificationTemplate
    }
  }
  ${CHEMO_VERIFICATION_TEMPLATE_FRAGMENT}
`;

export const SAVE_CHEMO_VERIFICATION_TEMPLATE = gql`
  mutation SaveChemoVerificationTemplate($input: NewChemoDiagnosisTemplateInput!) {
    saveChemoDiagnosisTemplate(input: $input) {
      ...ChemoVerificationTemplate
    }
  }
  ${CHEMO_VERIFICATION_TEMPLATE_FRAGMENT}
`;

export const UPDATE_CHEMO_VERIFICATION_TEMPLATE = gql`
  mutation UpdateChemoVerificationTemplate($input: ChemoDiagnosisTemplateInput!, $id: String!) {
    updateChemoDiagnosisTemplate(input: $input, id: $id) {
      ...ChemoVerificationTemplate
    }
  }
  ${CHEMO_VERIFICATION_TEMPLATE_FRAGMENT}
`;

export const DELETE_CHEMO_VERIFICATION_TEMPLATE = gql`
  mutation DeleteChemoVerificationTemplate($id: String!) {
    deleteChemoDiagnosisTemplate(id: $id) {
      id
    }
  }
`;

export const UPDATE_FACILITY_TARIFFS_TO_USE = gql`
  mutation UpdateTariffsToUse($facilityPreferenceId: String!, $useHQFacilityTariffs: Boolean!) {
    updateTariffsToUse(
      facilityPreferenceId: $facilityPreferenceId
      useHQFacilityTariffs: $useHQFacilityTariffs
    ) {
      id
      useHQFacilityTariffs
    }
  }
`;

export const UPDATE_FACILITY_INVENTORY_TO_USE = gql`
  mutation UpdateInventoryToUse($facilityPreferenceId: String!, $useHQFacilityInventory: Boolean!) {
    updateInventoryToUse(
      facilityPreferenceId: $facilityPreferenceId
      useHQFacilityInventory: $useHQFacilityInventory
    ) {
      id
      useHQFacilityInventory
    }
  }
`;

export const GET_FACILITY_BRANCHES = gql`
  query GetFacilityBranches($hospitalId: String!) {
    hospital(hospitalId: $hospitalId) {
      id
      branches {
        ...FacilityBranchFragment
      }
    }
  }
  ${FACILITY_BRANCH_FRAGMENT}
`;

export const UPDATE_FACILITY_BRANCH_INFORMATION = gql`
  mutation UpdateFacilityBranchInformation($hospitalId: String!, $input: BranchInformationInput!) {
    updateBranchInformation(hospitalId: $hospitalId, input: $input) {
      ...FacilityBranchFragment
    }
  }
  ${FACILITY_BRANCH_FRAGMENT}
`;

export const GET_TARIFFS_AND_INVENTORY_PREFERENCE = gql`
  query GetTariffsAndInventoryPreference {
    hospital {
      id
      preference {
        id
        useHQFacilityTariffs
        useHQFacilityInventory
      }
    }
  }
`;

export const UPDATE_SPECIALIST_ACCESS = gql`
  mutation UpdateSpecialistAccess($patientId: String!, $specialistIds: [String!]!) {
    updateSpecialistAccess(patientId: $patientId, specialistIds: $specialistIds) {
      id
      profile {
        id
      }
      specialistIds
    }
  }
`;

export const GET_SPECIALIST_ACCESS = gql`
  query GetSpecialistAccess($patientId: String!) {
    getSpecialistAccess(patientId: $patientId) {
      id
      profile {
        id
      }
      specialistIds
    }
  }
`;

export const GET_SPECIALIST_ACCESS_BY_HOSPITAL = gql`
  query GetSpecialistAccessByHospital {
    findByHospitalIdSpecialistAccess {
      id
      profile {
        id
      }
      specialistIds
    }
  }
`;

export const SAVE_OPERATION_NOTE_TEMPLATE = gql`
  mutation SaveOperationNoteTemplate($input: NewOperationNoteTemplateInput!) {
    saveOperationNoteTemplate(input: $input) {
      id
      name
      note
      postNote
    }
  }
`;

export const UPDATE_OPERATION_NOTE_TEMPLATE = gql`
  mutation UpdateOperationNoteTemplate($input: OperationNoteTemplateInput!) {
    updateOperationNoteTemplate(input: $input) {
      id
      name
      note
      postNote
    }
  }
`;

export const DELETE_OPERATION_NOTE_TEMPLATE = gql`
  mutation DeleteOperationNoteTemplate($id: String!) {
    deleteOperationNoteTemplate(id: $id) {
      id
    }
  }
`;

export const FETCH_OPERATION_NOTE_TEMPLATES = gql`
  query FetchOperationNoteTemplates($facilityPreferenceId: String) {
    fetchOperationNoteTemplates(facilityPreferenceId: $facilityPreferenceId) {
      id
      name
      note
      postNote
    }
  }
`;

export const UPDATE_FACILITY_RECORD_VISIBILITY = gql`
  mutation UpdateFacilityRecordVisibility($visibility: Boolean!) {
    updateFacilityRecordVisibility(visibility: $visibility)
  }
`;

export const GET_FACILITY_RECORD_VISIBILITY = gql`
  query GetFacilityRecordVisibility {
    getFacilityRecordVisibility
  }
`;

const BUSINESS_RULE_FIELDS = gql`
  fragment BusinessRule on BusinessRuleModel {
    id
    flag
    matchAll
    sumAll
    items {
      id
      type
      category
      operator
      value
       extra {
        frequencyUnit
        frequencyTarget
        frequencyTargetValue
        frequencyTargetUnit
        frequencyTargetQuantity
        frequencyTargetOperator
      }
    }
   
    ${AUDIT_FIELDS}
  }
`;

export const FETCH_BUSINESS_RULE = gql`
  query FetchBusinessRules {
    fetchBusinessRules {
      ...BusinessRule
    }
  }
  ${BUSINESS_RULE_FIELDS}
`;

export const CREATE_BUSINESS_RULE = gql`
  mutation CreateBusinessRule($rules: [NewBusinessRuleInput!]!) {
    createBusinessRule(rules: $rules) {
      ...BusinessRule
    }
  }
  ${BUSINESS_RULE_FIELDS}
`;

export const UPDATE_BUSINESS_RULE = gql`
  mutation UpdateBusinessRule($rules: UpdateBusinessRuleInput!) {
    updateBusinessRule(rules: $rules) {
      ...BusinessRule
    }
  }
  ${BUSINESS_RULE_FIELDS}
`;

export const DELETE_BUSINESS_RULE = gql`
  mutation DeleteBusinessRule($id: String!) {
    deleteBusinessRule(id: $id) {
      id
    }
  }
`;

export const GET_BUSINESS_RULE_VISITATION_TYPES = gql`
  query GetBusinessRuleVisitationTypes($filterOptions: BusinessRuleFilterInput!) {
    getBusinessRuleVisitationTypes(filterOptions: $filterOptions)
  }
`;

export const GET_BUSINESS_RULE_UTILIZATION_TYPES = gql`
  query GetBusinessRuleUtilizationTypes($filterOptions: BusinessRuleFilterInput!) {
    getBusinessRuleUtilizationTypes(filterOptions: $filterOptions)
  }
`;

export const GET_BUSINESS_RULE_UTILIZATION_CATEGORIES = gql`
  query GetBusinessRuleUtilizationCategories($filterOptions: BusinessRuleFilterInput!) {
    getBusinessRuleUtilizationCategories(filterOptions: $filterOptions)
  }
`;

export const GET_HOSPITAL_BY_PROVIDER_CODE = gql`
  query getHospitalByProviderCode($providerCode: String!, $filterOptions: HmoFilterOptions) {
    getHospitalByProviderCode(providerCode: $providerCode, filterOptions: $filterOptions) {
      totalCount
      list {
        id
        name
        address
      }
    }
  }
`;

export const UPDATE_PATIENT_REGISTRATION_FEE = gql`
  mutation UpdatePatientRegistrationFee($registrationFee: Float!) {
    updatePatientRegistrationFee(registrationFee: $registrationFee) {
      id
      registrationFee
    }
  }
`;

export const FETCH_PREFERRED_PAYOUT_BANK_ACCOUNT = gql`
  query FetchPreferredPayoutBankAccount($hospitalId: String!) {
    getPreferredPayoutAccount(hospitalId: $hospitalId) {
      accountName
      accountNumber
      bankName
    }
  }
`;

export const UPDATE_ENROLLEE_CAPITAION_AMOUNT = gql`
  mutation UpdateEnrolleeCapitationAmount($enrolleeCapitationAmount: Float!) {
    updateEnrolleeCapitationAmount(enrolleeCapitationAmount: $enrolleeCapitationAmount) {
      id
      enrolleeCapitationAmount
    }
  }
`;

export const UPDATE_ENROLLEE_CAPITAION_AMOUNT_PER_PLAN = gql`
  mutation UpdateEnrolleeCapitationAmountPerPlan($enabled: Boolean!) {
    updateEnrolleeCapitationAmountPerPlan(enabled: $enabled) {
      id
      enrolleeCapitationAmountPerPlan
    }
  }
`;

export const UPDATE_ENROLLEE_CAPITAION_AMOUNT_BY_PLAN_TYPE = gql`
  mutation UpdateEnrolleeCapitationAmountByPlanType(
    $planTypeId: String!
    $planTypeName: String!
    $capitationAmount: Float!
  ) {
    updateEnrolleeCapitationAmountByPlanType(
      planTypeId: $planTypeId
      planTypeName: $planTypeName
      capitationAmount: $capitationAmount
    ) {
      id
      enrolleeCapitionAmountByPlanType {
        planTypeId
        planTypeName
        amount
      }
    }
  }
`;

export const GET_TOTAL_PROVIDERS_COUNT = gql`
  query GetTotalProvidersCount {
    getTotalProvidersCount
  }
`;

export const GET_CAPITATED_PROVIDER_SUMMARY = gql`
  query GetCapitatedProviderSummary(
    $startDate: String
    $endDate: String
    $planId: String
    $hospitalId: String
  ) {
    getCapitatedProviderSummary(
      startDate: $startDate
      endDate: $endDate
      planId: $planId
      hospitalId: $hospitalId
    ) {
      totalCapitatedProvidersCount
      totalCapitatedEnrolleeCount
      totalCapitatedAmount
    }
  }
`;

export const GET_CAPITATED_ENROLLEE_SUMMARY = gql`
  query GetCapitatedEnrolleeSummary(
    $startDate: String
    $endDate: String
    $hmoProviderId: String
    $registeredWith: String
    $planId: String
  ) {
    getCapitatedEnrolleeSummary(
      startDate: $startDate
      endDate: $endDate
      hmoProviderId: $hmoProviderId
      registeredWith: $registeredWith
      planId: $planId
    ) {
      totalEnrolleeCount
      totalActiveEnrolleeCount
      totalInactiveEnrolleeCount
      totalExpiredEnrolleeCount
      totalCapitatedEnrolleeCount
      totalCapitatedAmount
    }
  }
`;

export const AGENCY_PROVIDER_FIELDS = gql`
  fragment AgencyProviderFields on HospitalModel {
    id
    clinifyId
    name
    address
    ownership
    lga
    politicalWard
    city
    state
    level
    supportMail
    country
    plan
    planStatus
    classification
    hmoHospitals {
      id
      hmoProviderUniqueId
      hmoProviderId
      tariffBand
      category
      enrolleeCount
      enrolleeLimit
      provider {
        id
      }
      planVisibility {
        id
        name
      }
    }
    phoneNumber {
      countryCode
      value
    }
    secondaryPhoneNumber {
      countryCode
      value
    }
    orgAdmin {
      id
      title
      fullName
      personalInformation {
        firstName
        middleName
        lastName
      }
      user {
        id
        email
      }
    }
  }
`;

export const GET_PROVIDER_DETAILS = gql`
  query GetProviderDetails($filterOptions: HospitalFilterInput) {
    hospitals(filterOptions: $filterOptions) {
      totalCount
      list {
        ...AgencyProviderFields
      }
    }
  }
  ${AGENCY_PROVIDER_FIELDS}
`;

export const REGISTER_NEW_PROVIDER = gql`
  mutation CreateProvider($createProviderInput: ProviderRegistrationInput!) {
    createProvider(createProviderInput: $createProviderInput) {
      ...AgencyProviderFields
    }
  }
  ${AGENCY_PROVIDER_FIELDS}
`;

export const UPDATE_REGISTERED_PROVIDER = gql`
  mutation UpdateProvider($hospitalId: String!, $updateProviderInput: UpdateProviderInput!) {
    updateProvider(hospitalId: $hospitalId, updateProviderInput: $updateProviderInput) {
      ...AgencyProviderFields
    }
  }
  ${AGENCY_PROVIDER_FIELDS}
`;

export const UPDATE_AUTO_PROCESS_CLAIMS_PREFERENCE = gql`
  mutation UpdateAutoProcessClaimsPreference($autoProcessClaimsPreference: Boolean!) {
    updateAutoProcessClaims(mode: $autoProcessClaimsPreference) {
      id
      autoProcessClaims
    }
  }
`;

export const UPDATE_AUTO_PROCESS_PREAUTHORIZATIONS_PREFERENCE = gql`
  mutation UpdateAutoProcessPreauthorizationsPreference(
    $autoProcessPreauthorizationsPreference: Boolean!
  ) {
    updateAutoProcessPreauthorizations(mode: $autoProcessPreauthorizationsPreference) {
      id
      autoProcessPreauthorizations
    }
  }
`;

export const UPDATE_ENROLLMENT_AGENT_ASSIGNMENTS = gql`
  mutation UpdateEnrollmentAgentAssignments($agent: EnrollmentAgentInput!) {
    updateEnrollmentAgentAssignments(agent: $agent) {
      id
      enrollmentAgentAssigments {
        profileId
        administrationAgency
        enrollmentTpa
        enrollmentAgency
        accountNumber
        accountName
        bankName
        bvn
        branchName
        status
      }
    }
  }
`;

export const UPDATE_ENROLLMENT_AGENCY = gql`
  mutation UpdateEnrollmentAgency($administrationAgency: String!, $enrollmentAgency: [String!]!) {
    updateEnrollmentAgency(
      administrationAgency: $administrationAgency
      enrollmentAgency: $enrollmentAgency
    ) {
      id
      enrollmentAgency {
        administrationAgency
        agencies
      }
    }
  }
`;

export const UPDATE_FIELD_OFFICER_ASSIGNMENTS = gql`
  mutation UpdateFieldOfficerAgentAssignments($fieldOfficer: FieldOfficerResponseInput!) {
    updateFieldOfficer(fieldOfficer: $fieldOfficer) {
      id
      fieldOfficers {
        profileId
        administrationAgency
        enrollmentAgency
        accountNumber
        accountName
        bankName
        bvn
        branchName
        status
      }
    }
  }
`;

export const ADD_SPONSOR_BILLING_INFORMATION = gql`
  mutation AddSponsorBillingInformation(
    $hospitalId: String!
    $input: SponsorBillingInformationInput!
  ) {
    addSponsorBillingInformation(hospitalId: $hospitalId, input: $input) {
      id
      accountName
      accountNumber
      bankName
      branchName
      bvn
      sponsorName
      hospitalId
      createdDate
      updatedDate
      creatorName
      lastModifierName
    }
  }
`;

export const UPDATE_ENROLLEE_SPONSORS = gql`
  mutation UpdateEnrolleeSponsors($sponsors: [String!]!) {
    updateEnrolleeSponsors(sponsors: $sponsors) {
      id
      enrolleeSponsors
    }
  }
`;

export const UPDATE_SPONSOR_ASSIGNMENTS = gql`
  mutation UpdateSponsorAssignment($sponsor: SponsorResponseInput!) {
    updateSponsorAssignment(sponsor: $sponsor) {
      id
      enrolleeSponsorAssigments {
        ref
        sponsorName
        sponsorType
        sponsorLives
        agencyLives
        amountDue
        paymentFrequency
        nextRenewalDate
        renewalCount
        paymentStatus
        paymentDateTime
        sponsoredPremiumPerLife
        totalSponsoredPremium
        status
      }
    }
  }
`;

export const UPDATE_SPONSOR_BILLING_INFORMATION = gql`
  mutation UpdateSponsorBillingInformation(
    $billingInformationId: String!
    $input: SponsorBillingInformationInput!
  ) {
    updateSponsorBillingInformation(billingInformationId: $billingInformationId, input: $input) {
      id
      accountName
      accountNumber
      bankName
      branchName
      bvn
      sponsorName
      hospitalId
      createdDate
      updatedDate
      creatorName
      lastModifierName
    }
  }
`;

export const UPDATE_ENROLLEE_REFERRALS = gql`
  mutation UpdateEnrolleeReferral($enrolleeReferral: EnrolleeReferralResponseInput!) {
    updateEnrolleeReferral(enrolleeReferral: $enrolleeReferral) {
      id
      enrolleeReferrals {
        name
        referrerCode
        accountNumber
        accountName
        bankName
        bvn
        branchName
        email
        status
        phoneNumber {
          countryCode
          value
          countryName
        }
      }
    }
  }
`;
export const DELETE_SPONSOR_BILLING_INFORMATION = gql`
  mutation DeleteSponsorBillingInformation($billingInformationId: String!) {
    deleteSponsorBillingInformation(billingInformationId: $billingInformationId) {
      id
      accountName
      accountNumber
      bankName
      sponsorName
      hospitalId
    }
  }
`;

export const UPDATE_PAYOUT_COMMISSION_PAYER = gql`
  mutation UpdatePayoutCommissionPayer($payoutCommissionPayer: PayoutCommissionPayer!) {
    updatePayoutCommissionPayer(payoutCommissionPayer: $payoutCommissionPayer) {
      id
      payoutCommissionPayer
    }
  }
`;

export const GET_BILLING_INFORMATION_BY_HOSPITAL_ID = gql`
  query GetBillingInformationByHospitalId($hospitalId: String!) {
    billingInformationByHospitalId(hospitalId: $hospitalId) {
      ...FacilityBillingInformation
    }
  }
  ${FACILITY_BILLING_INFORMATION_FRAGMENT}
`;

export const UPDATE_FACILITY_STAFF_INFORMATION = gql`
  mutation UpdateFacilityStaffInformation(
    $facilityId: String!
    $input: EditHospitalStaffInput!
    $profileId: String!
  ) {
    updateFacilityStaffInformation(facilityId: $facilityId, input: $input, profileId: $profileId) {
      id
      gender
      title
      fullName
      personalInformation {
        firstName
        middleName
        lastName
        rank
        speciality
        department
        userRole
      }
      user {
        id
        phoneNumber
        email
      }
    }
  }
`;

export const UPDATE_ENROLLEE_TPA_ASSIGNMENTS = gql`
  mutation UpdateEnrolleeTpaAssignment($tpaInput: TpaResponseInput!) {
    updateEnrolleeTpaAssignment(tpaInput: $tpaInput) {
      id
      enrollmentTpaAssigments {
        ref
        name
        address
        isTpa
        country
        state
        localGovernmentArea
        primaryPhoneNumber {
          countryCode
          value
          countryName
        }
        primaryEmailAddress
        secondaryPhoneNumber {
          countryCode
          value
          countryName
        }
        secondaryEmailAddress
        startDate
        endDate
        renewalDate
        contactPersonTitle
        contactPersonFirstName
        contactPersonMiddleName
        contactPersonLastName
        contactPersonPhoneNumber {
          countryCode
          value
          countryName
        }
        contactPersonEmailAddress
        contactPersonAltTitle
        contactPersonAltFirstName
        contactPersonAltMiddleName
        contactPersonAltLastName
        contactPersonAltPhoneNumber {
          countryCode
          value
          countryName
        }
        contactPersonAltEmailAddress
        tpaNumber
        tpaCode
        accountName
        accountNumber
        bankName
        bvn
        branchName
        status
      }
    }
  }
`;
