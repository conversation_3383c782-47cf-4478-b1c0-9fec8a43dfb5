/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { Gender, Subject, Action } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: DeleteHospitalStaffForFacility
// ====================================================

export interface DeleteHospitalStaffForFacility_deleteHospitalStaffForFacility_personalInformation_coverageDetails_questionnaireData {
  __typename: "QuestionnaireData";
  employmentStatus: string | null;
  gender: string | null;
  highestFormalEducationalLevel: string | null;
  maritalStatus: string | null;
  numberOfHouseholdMembers: string | null;
  numberOfMattresses: string | null;
  numberOfMobilePhones: string | null;
  numberOfRooms: string | null;
  numberOfTVs: string | null;
  numberOfVehicles: string | null;
  occupationalGroup: string | null;
  occupation: string | null;
  primaryCookingImplement: string | null;
  relationshipToHouseholdHead: string | null;
  typeOfRoof: string | null;
  typeOfToilet: string | null;
  /**
   * Score in percentage
   */
  questionnaireScore: number | null;
}

export interface DeleteHospitalStaffForFacility_deleteHospitalStaffForFacility_personalInformation_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface DeleteHospitalStaffForFacility_deleteHospitalStaffForFacility_personalInformation_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  coverageType: string | null;
  companyName: string | null;
  companyAddress: string | null;
  familyName: string | null;
  familyAddress: string | null;
  name: string | null;
  contactAddress: string | null;
  memberNumber: string | null;
  memberPlan: string | null;
  employeeNumber: string | null;
  capturedDate: any | null;
  capturedBy: string | null;
  enrolledBy: string | null;
  enrollmentDateTime: any | null;
  paymentDateTime: any | null;
  paymentFrequency: string | null;
  memberStartDate: any | null;
  memberDueDate: any | null;
  memberStatus: string | null;
  memberUniqueId: string | null;
  parentMemberUniqueId: string | null;
  primaryProviderId: string | null;
  primaryProviderName: string | null;
  secondaryProviderName: string | null;
  tertiaryProviderName: string | null;
  primaryProviderAddress: string | null;
  secondaryProviderAddress: string | null;
  tertiaryProviderAddress: string | null;
  capitatedMember: string | null;
  capitatedAmount: string | null;
  employeeType: string | null;
  employeeDivision: string | null;
  occupation: string | null;
  retired: boolean | null;
  memberPlanGroup: string | null;
  memberPlanSubGroup: string | null;
  terminationDate: any | null;
  premiumCollected: string | null;
  premiumOutstanding: string | null;
  registrationSource: string | null;
  administrationAgency: string | null;
  commissionPayable: string | null;
  enrollmentAgent: string | null;
  enrollmentAgency: string | null;
  commissionRate: string | null;
  planCategory: string | null;
  salesWing: string | null;
  sponsorType: string | null;
  sponsorName: string | null;
  referrer: string | null;
  referrerCode: string | null;
  referrerNumber: string | null;
  referrerCommissionRate: string | null;
  referrerCommissionPayable: string | null;
  employerCode: string | null;
  activationDatetime: any | null;
  totalPremiumAmountPaid: string | null;
  questionnaireData: DeleteHospitalStaffForFacility_deleteHospitalStaffForFacility_personalInformation_coverageDetails_questionnaireData | null;
  provider: DeleteHospitalStaffForFacility_deleteHospitalStaffForFacility_personalInformation_coverageDetails_provider | null;
}

export interface DeleteHospitalStaffForFacility_deleteHospitalStaffForFacility_personalInformation_secondaryPhoneNumber {
  __typename: "PhoneNumberFields";
  value: string | null;
  countryCode: string | null;
  countryName: string | null;
}

export interface DeleteHospitalStaffForFacility_deleteHospitalStaffForFacility_personalInformation {
  __typename: "PersonalInformation";
  id: string | null;
  rank: string | null;
  title: string | null;
  department: string | null;
  speciality: string | null;
  displayPictureUrl: string | null;
  firstName: string | null;
  lastName: string | null;
  middleName: string | null;
  dateOfBirth: any | null;
  bloodGroup: string | null;
  genoType: string | null;
  weight: string | null;
  weightUnit: string | null;
  height: string | null;
  heightUnit: string | null;
  address: string | null;
  gender: Gender | null;
  secondaryEmail: string | null;
  folioNumber: string | null;
  clinicalTrials: string | null;
  patientFileOrCardNo: string | null;
  coverageDetails: DeleteHospitalStaffForFacility_deleteHospitalStaffForFacility_personalInformation_coverageDetails[] | null;
  secondaryPhoneNumber: DeleteHospitalStaffForFacility_deleteHospitalStaffForFacility_personalInformation_secondaryPhoneNumber | null;
  nin: string | null;
  votersId: string | null;
  passportNumber: string | null;
  bvn: string | null;
  registrationNote: string | null;
  lga: string | null;
  ward: string | null;
  buildingName: string | null;
  buildingLevel: string | null;
  countryOfResidence: string | null;
  stateOfResidence: string | null;
  patientCategory: string[] | null;
  nationality: string | null;
  state: string | null;
  originLga: string | null;
  city: string | null;
  placeOfBirth: string | null;
  userRole: string | null;
}

export interface DeleteHospitalStaffForFacility_deleteHospitalStaffForFacility_user {
  __typename: "UserModel";
  id: string;
  email: string | null;
  corporatePhoneNumber: string | null;
  country: string | null;
}

export interface DeleteHospitalStaffForFacility_deleteHospitalStaffForFacility_permissions_rules {
  __typename: "PermissionModelInput";
  subject: Subject;
  action: Action;
}

export interface DeleteHospitalStaffForFacility_deleteHospitalStaffForFacility_permissions {
  __typename: "PermissionModel";
  id: string;
  rules: DeleteHospitalStaffForFacility_deleteHospitalStaffForFacility_permissions_rules[] | null;
}

export interface DeleteHospitalStaffForFacility_deleteHospitalStaffForFacility {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  active: boolean;
  fullName: string;
  type: string;
  personalInformation: DeleteHospitalStaffForFacility_deleteHospitalStaffForFacility_personalInformation | null;
  user: DeleteHospitalStaffForFacility_deleteHospitalStaffForFacility_user;
  permissions: DeleteHospitalStaffForFacility_deleteHospitalStaffForFacility_permissions;
}

export interface DeleteHospitalStaffForFacility {
  deleteHospitalStaffForFacility: DeleteHospitalStaffForFacility_deleteHospitalStaffForFacility[];
}

export interface DeleteHospitalStaffForFacilityVariables {
  hospitalId: string;
  profileIds: string[];
}
