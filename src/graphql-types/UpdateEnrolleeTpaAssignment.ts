/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { TpaResponseInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateEnrolleeTpaAssignment
// ====================================================

export interface UpdateEnrolleeTpaAssignment_updateEnrolleeTpaAssignment_enrollmentTpaAssigments_primaryPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface UpdateEnrolleeTpaAssignment_updateEnrolleeTpaAssignment_enrollmentTpaAssigments_secondaryPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface UpdateEnrolleeTpaAssignment_updateEnrolleeTpaAssignment_enrollmentTpaAssigments_contactPersonPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface UpdateEnrolleeTpaAssignment_updateEnrolleeTpaAssignment_enrollmentTpaAssigments_contactPersonAltPhoneNumber {
  __typename: "PhoneNumberFields";
  countryCode: string | null;
  value: string | null;
  countryName: string | null;
}

export interface UpdateEnrolleeTpaAssignment_updateEnrolleeTpaAssignment_enrollmentTpaAssigments {
  __typename: "TpaResponseDto";
  ref: string | null;
  name: string | null;
  address: string | null;
  isTpa: boolean | null;
  country: string | null;
  state: string | null;
  localGovernmentArea: string | null;
  primaryPhoneNumber: UpdateEnrolleeTpaAssignment_updateEnrolleeTpaAssignment_enrollmentTpaAssigments_primaryPhoneNumber | null;
  primaryEmailAddress: string | null;
  secondaryPhoneNumber: UpdateEnrolleeTpaAssignment_updateEnrolleeTpaAssignment_enrollmentTpaAssigments_secondaryPhoneNumber | null;
  secondaryEmailAddress: string | null;
  startDate: any | null;
  endDate: any | null;
  renewalDate: any | null;
  contactPersonTitle: string | null;
  contactPersonFirstName: string | null;
  contactPersonMiddleName: string | null;
  contactPersonLastName: string | null;
  contactPersonPhoneNumber: UpdateEnrolleeTpaAssignment_updateEnrolleeTpaAssignment_enrollmentTpaAssigments_contactPersonPhoneNumber | null;
  contactPersonEmailAddress: string | null;
  contactPersonAltTitle: string | null;
  contactPersonAltFirstName: string | null;
  contactPersonAltMiddleName: string | null;
  contactPersonAltLastName: string | null;
  contactPersonAltPhoneNumber: UpdateEnrolleeTpaAssignment_updateEnrolleeTpaAssignment_enrollmentTpaAssigments_contactPersonAltPhoneNumber | null;
  contactPersonAltEmailAddress: string | null;
  tpaNumber: string | null;
  tpaCode: string | null;
  accountName: string | null;
  accountNumber: string | null;
  bankName: string | null;
  bvn: string | null;
  branchName: string | null;
  status: string | null;
}

export interface UpdateEnrolleeTpaAssignment_updateEnrolleeTpaAssignment {
  __typename: "FacilityPreferenceModel";
  id: string;
  enrollmentTpaAssigments: UpdateEnrolleeTpaAssignment_updateEnrolleeTpaAssignment_enrollmentTpaAssigments[] | null;
}

export interface UpdateEnrolleeTpaAssignment {
  updateEnrolleeTpaAssignment: UpdateEnrolleeTpaAssignment_updateEnrolleeTpaAssignment;
}

export interface UpdateEnrolleeTpaAssignmentVariables {
  tpaInput: TpaResponseInput;
}
