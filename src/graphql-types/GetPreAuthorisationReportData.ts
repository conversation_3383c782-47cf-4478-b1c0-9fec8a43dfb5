/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { HmosAnalyticsFilter } from "./globalTypes";

// ====================================================
// GraphQL query operation: GetPreAuthorisationReportData
// ====================================================

export interface GetPreAuthorisationReportData_getPreAuthorisationReportData {
  __typename: "AgencyPreAuthorisationReportData";
  claimId: string | null;
  enrolleeNumber: string | null;
  enrolleeFirstName: string;
  enrolleeLastName: string | null;
  enrolleeMiddleName: string | null;
  enrolleePhoneNumber: string | null;
  enrolleeGender: string | null;
  diagnosisCode: string | null;
  diagnosisName: string | null;
  providerCode: string | null;
  providerName: string | null;
  utilizationCode: string | null;
  utilizationType: string | null;
  utilizationCategory: string;
  requestedBy: string | null;
  paCode: string;
  requestedDate: string | null;
  visitType: string | null;
  treatmentDate: string | null;
  legacyId: string | null;
  dischargeDate: string | null;
  batchNumber: string | null;
  amount: string | null;
  primaryProviderName: string | null;
  verifiedBy: string | null;
  planType: string | null;
  referralCode: string | null;
  referralProviderCode: string | null;
  referralProviderName: string | null;
  approvedBy: string | null;
  visitId: string | null;
  tpa: string | null;
  turnaroundTime: string | null;
  paStatus: string | null;
}

export interface GetPreAuthorisationReportData {
  getPreAuthorisationReportData: GetPreAuthorisationReportData_getPreAuthorisationReportData[];
}

export interface GetPreAuthorisationReportDataVariables {
  filter: HmosAnalyticsFilter;
}
