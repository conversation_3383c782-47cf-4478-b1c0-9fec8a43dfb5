/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FundTransactionStatus, PayoutCommissionPayer } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateCapitatedEnrolleeCount
// ====================================================

export interface UpdateCapitatedEnrolleeCount_updateCapitationEnrolleeCount_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  type: string;
  clinifyId: string;
  title: string | null;
}

export interface UpdateCapitatedEnrolleeCount_updateCapitationEnrolleeCount_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface UpdateCapitatedEnrolleeCount_updateCapitationEnrolleeCount_auditApproval {
  __typename: "ClaimsAccountApprovalType";
  createdDate: any;
  creatorName: string;
  approvalGroup: string;
  creatorId: string;
}

export interface UpdateCapitatedEnrolleeCount_updateCapitationEnrolleeCount_hmoPlanType {
  __typename: "HmoPlanTypeModel";
  id: string;
  name: string | null;
}

export interface UpdateCapitatedEnrolleeCount_updateCapitationEnrolleeCount_detailsByPlanType {
  __typename: "CapitationDetailByPlanType";
  enrolleeCount: number;
  planType: string;
  planTypeName: string;
  totalCapitationAmount: number;
  payoutDecreasePercentage: number | null;
}

export interface UpdateCapitatedEnrolleeCount_updateCapitationEnrolleeCount {
  __typename: "TransferFundModel";
  id: string;
  createdBy: UpdateCapitatedEnrolleeCount_updateCapitationEnrolleeCount_createdBy | null;
  amount: number;
  createdDate: any;
  updatedDate: any;
  destinationAccountName: string | null;
  destinationAccountNumber: string | null;
  destinationBankCode: string | null;
  destinationBankName: string | null;
  narration: string | null;
  originatorName: string | null;
  sourceAccountNumber: string | null;
  transferReference: string | null;
  transferStatus: FundTransactionStatus;
  additionalNote: string | null;
  serviceChargeAmount: number | null;
  hospitalId: string | null;
  hmoPlanTypeId: string | null;
  hospital: UpdateCapitatedEnrolleeCount_updateCapitationEnrolleeCount_hospital | null;
  enrolleeCount: number | null;
  isEnrolleePayout: boolean | null;
  totalCapitationAmount: number | null;
  auditApproval: UpdateCapitatedEnrolleeCount_updateCapitationEnrolleeCount_auditApproval[] | null;
  hmoPlanType: UpdateCapitatedEnrolleeCount_updateCapitationEnrolleeCount_hmoPlanType | null;
  detailsByPlanType: UpdateCapitatedEnrolleeCount_updateCapitationEnrolleeCount_detailsByPlanType[] | null;
  payoutCommissionPayer: PayoutCommissionPayer | null;
  payoutDecreasePercentage: number | null;
}

export interface UpdateCapitatedEnrolleeCount {
  updateCapitationEnrolleeCount: UpdateCapitatedEnrolleeCount_updateCapitationEnrolleeCount[];
}

export interface UpdateCapitatedEnrolleeCountVariables {
  hospitalId: string;
  startDate: string;
  endDate: string;
  enrolleeCount: number;
  hmoPlanTypeId?: string | null;
}
