/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { UpdatePreAuthUtilizationStatusInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: ProcessPreauthUtilizationStatus
// ====================================================

export interface ProcessPreauthUtilizationStatus_updatePreAuthUtilizationStatus_utilisationStatus {
  __typename: "ClaimsApprovalInputType";
  status: string | null;
  rejectionReason: string[] | null;
  specifyReasonForRejection: string | null;
  statusDescription: string | null;
  serviceAmount: number | null;
  vettingGroup: string;
  creatorId: string;
  creatorName: string;
  createdDate: any;
  updatedDate: any | null;
  comment: string | null;
}

export interface ProcessPreauthUtilizationStatus_updatePreAuthUtilizationStatus {
  __typename: "PreAuthUtilisationsModel";
  id: string;
  paCode: string | null;
  status: string | null;
  rejectionReason: string[] | null;
  specifyReasonForRejection: string | null;
  statusDescription: string | null;
  lastModifierName: string | null;
  updatedDate: any;
  utilisationStatus: ProcessPreauthUtilizationStatus_updatePreAuthUtilizationStatus_utilisationStatus[] | null;
}

export interface ProcessPreauthUtilizationStatus {
  updatePreAuthUtilizationStatus: ProcessPreauthUtilizationStatus_updatePreAuthUtilizationStatus;
}

export interface ProcessPreauthUtilizationStatusVariables {
  input: UpdatePreAuthUtilizationStatusInput;
}
