/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { EnrollmentAgentInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateEnrollmentAgentAssignments
// ====================================================

export interface UpdateEnrollmentAgentAssignments_updateEnrollmentAgentAssignments_enrollmentAgentAssigments {
  __typename: "EnrollmentAgentDto";
  profileId: string | null;
  administrationAgency: string | null;
  enrollmentTpa: string | null;
  enrollmentAgency: string | null;
  accountNumber: string | null;
  accountName: string | null;
  bankName: string | null;
  bvn: string | null;
  branchName: string | null;
  status: string | null;
}

export interface UpdateEnrollmentAgentAssignments_updateEnrollmentAgentAssignments {
  __typename: "FacilityPreferenceModel";
  id: string;
  enrollmentAgentAssigments: UpdateEnrollmentAgentAssignments_updateEnrollmentAgentAssignments_enrollmentAgentAssigments[] | null;
}

export interface UpdateEnrollmentAgentAssignments {
  updateEnrollmentAgentAssignments: UpdateEnrollmentAgentAssignments_updateEnrollmentAgentAssignments;
}

export interface UpdateEnrollmentAgentAssignmentsVariables {
  agent: EnrollmentAgentInput;
}
