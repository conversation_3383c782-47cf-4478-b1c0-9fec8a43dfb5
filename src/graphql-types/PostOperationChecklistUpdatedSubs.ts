/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL subscription operation: PostOperationChecklistUpdatedSubs
// ====================================================

export interface PostOperationChecklistUpdatedSubs_PostOperationChecklistUpdated_dietOrderSheet {
  __typename: "OrderDetailType";
  checked: boolean | null;
  orderName: string | null;
  date: any | null;
  checkedBy: string | null;
}

export interface PostOperationChecklistUpdatedSubs_PostOperationChecklistUpdated_ambutationOrderSheet {
  __typename: "OrderDetailType";
  checked: boolean | null;
  orderName: string | null;
  date: any | null;
  checkedBy: string | null;
}

export interface PostOperationChecklistUpdatedSubs_PostOperationChecklistUpdated_urethralCatheterizationOrderSheet {
  __typename: "OrderDetailType";
  checked: boolean | null;
  orderName: string | null;
  date: any | null;
  checkedBy: string | null;
}

export interface PostOperationChecklistUpdatedSubs_PostOperationChecklistUpdated_fluidTherapyOrderSheet {
  __typename: "OrderDetailType";
  checked: boolean | null;
  orderName: string | null;
  date: any | null;
  checkedBy: string | null;
}

export interface PostOperationChecklistUpdatedSubs_PostOperationChecklistUpdated_antiBioticsOrderSheet {
  __typename: "OrderDetailType";
  checked: boolean | null;
  orderName: string | null;
  date: any | null;
  checkedBy: string | null;
}

export interface PostOperationChecklistUpdatedSubs_PostOperationChecklistUpdated_analgesicOrderSheet {
  __typename: "OrderDetailType";
  checked: boolean | null;
  orderName: string | null;
  date: any | null;
  checkedBy: string | null;
  title: string | null;
  names: string[] | null;
  isMultiple: boolean | null;
}

export interface PostOperationChecklistUpdatedSubs_PostOperationChecklistUpdated_clexane40mgFor3DaysOrderSheet {
  __typename: "OrderDetailType";
  checked: boolean | null;
  orderName: string | null;
  date: any | null;
  checkedBy: string | null;
}

export interface PostOperationChecklistUpdatedSubs_PostOperationChecklistUpdated_vitalSigns {
  __typename: "VitalSignType";
  timeIn: string | null;
  systolic: string | null;
  diastolic: string | null;
  pulseRate: string | null;
  oxygenSaturation: string | null;
  respiratoryRate: string | null;
  temperature: string | null;
  temperatureUnit: string | null;
  nausea: string | null;
  painScore: string | null;
  state: string | null;
}

export interface PostOperationChecklistUpdatedSubs_PostOperationChecklistUpdated_medications {
  __typename: "MedicationDetailType";
  medicationName: string | null;
  prescriptionTime: any | null;
  dosage: string | null;
  dosageUnit: string | null;
}

export interface PostOperationChecklistUpdatedSubs_PostOperationChecklistUpdated_urineOutput {
  __typename: "UrineOutputDetailType";
  timeIn: string | null;
  output: string | null;
}

export interface PostOperationChecklistUpdatedSubs_PostOperationChecklistUpdated_linesDrainsTubes {
  __typename: "LinesDrainsTubesDetailType";
  intravenousInfusions: string | null;
  others: string | null;
  dressingChecked: string | null;
  dressingCheckedTime: any | null;
  drainsChecked: string | null;
  drainsCheckedTime: any | null;
  catheterChecked: string | null;
  catheterCheckedTime: any | null;
}

export interface PostOperationChecklistUpdatedSubs_PostOperationChecklistUpdated_newOrders {
  __typename: "OrderDetailType";
  checked: boolean | null;
  orderName: string | null;
  date: any | null;
  checkedBy: string | null;
}

export interface PostOperationChecklistUpdatedSubs_PostOperationChecklistUpdated_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
}

export interface PostOperationChecklistUpdatedSubs_PostOperationChecklistUpdated {
  __typename: "PostOperationModel";
  id: string;
  serviceStartDateTime: any | null;
  chartType: string | null;
  surgeryStartDateTime: any | null;
  surgeryEndDateTime: any | null;
  dietOrderSheet: PostOperationChecklistUpdatedSubs_PostOperationChecklistUpdated_dietOrderSheet[] | null;
  ambutationOrderSheet: PostOperationChecklistUpdatedSubs_PostOperationChecklistUpdated_ambutationOrderSheet[] | null;
  urethralCatheterizationOrderSheet: PostOperationChecklistUpdatedSubs_PostOperationChecklistUpdated_urethralCatheterizationOrderSheet[] | null;
  fluidTherapyOrderSheet: PostOperationChecklistUpdatedSubs_PostOperationChecklistUpdated_fluidTherapyOrderSheet[] | null;
  antiBioticsOrderSheet: PostOperationChecklistUpdatedSubs_PostOperationChecklistUpdated_antiBioticsOrderSheet[] | null;
  analgesicOrderSheet: PostOperationChecklistUpdatedSubs_PostOperationChecklistUpdated_analgesicOrderSheet[] | null;
  clexane40mgFor3DaysOrderSheet: PostOperationChecklistUpdatedSubs_PostOperationChecklistUpdated_clexane40mgFor3DaysOrderSheet[] | null;
  vitalSigns: PostOperationChecklistUpdatedSubs_PostOperationChecklistUpdated_vitalSigns[] | null;
  medications: PostOperationChecklistUpdatedSubs_PostOperationChecklistUpdated_medications[] | null;
  discontinueMedication: boolean;
  urineOutput: PostOperationChecklistUpdatedSubs_PostOperationChecklistUpdated_urineOutput[] | null;
  linesDrainsTubes: PostOperationChecklistUpdatedSubs_PostOperationChecklistUpdated_linesDrainsTubes[] | null;
  newOrders: PostOperationChecklistUpdatedSubs_PostOperationChecklistUpdated_newOrders[] | null;
  SurgeonName: string | null;
  surgeonNameSignature: string | null;
  surgeonNameSignatureType: string | null;
  surgeonNameSignatureDateTime: any | null;
  SurgeonSpecialty: string | null;
  SurgeonAssistantName: string | null;
  surgeonAssistantNameSignature: string | null;
  surgeonAssistantNameSignatureType: string | null;
  surgeonAssistantNameSignatureDateTime: any | null;
  operatingRoomNurse: string | null;
  operatingRoomNurseSignature: string | null;
  operatingRoomNurseSignatureType: string | null;
  operatingRoomNurseSignatureDateTime: any | null;
  anesthetistName: string | null;
  anesthetistNameSignature: string | null;
  anesthetistNameSignatureType: string | null;
  anesthetistNameSignatureDateTime: any | null;
  recoveryNurse: string | null;
  recoveryNurseSignature: string | null;
  recoveryNurseSignatureType: string | null;
  recoveryNurseSignatureDateTime: any | null;
  visitingSpecialistName: string | null;
  visitingSpecialistSignature: string | null;
  visitingSpecialistSignatureType: string | null;
  visitingSpecialistSignatureDateTime: any | null;
  visitingFacilityName: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean;
  profileId: string | null;
  profile: PostOperationChecklistUpdatedSubs_PostOperationChecklistUpdated_profile | null;
}

export interface PostOperationChecklistUpdatedSubs {
  PostOperationChecklistUpdated: PostOperationChecklistUpdatedSubs_PostOperationChecklistUpdated;
}

export interface PostOperationChecklistUpdatedSubsVariables {
  profileId: string;
}
