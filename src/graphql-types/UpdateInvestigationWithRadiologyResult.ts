/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { InvestigationWithRadiologyInput, Gender, BillStatus, investigationStatus, specimenCollection, PatientType, BillableRecords } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: UpdateInvestigationWithRadiologyResult
// ====================================================

export interface UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_testInfo {
  __typename: "InvestigationTestInfoInputType";
  testName: string;
  priority: string | null;
  testCategory: string | null;
  specimen: string[] | null;
  clinicalDiagnosisICD10: string | null;
  clinicalDiagnosisICD11: string | null;
  clinicalDiagnosisSNOMED: string | null;
  loinc: string | null;
  ref: string | null;
  provider: string | null;
  itemId: string | null;
  preauthorizationDetailsId: string | null;
}

export interface UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_profile_personalInformation_coverageDetails_questionnaireData {
  __typename: "QuestionnaireData";
  employmentStatus: string | null;
  gender: string | null;
  highestFormalEducationalLevel: string | null;
  maritalStatus: string | null;
  numberOfHouseholdMembers: string | null;
  numberOfMattresses: string | null;
  numberOfMobilePhones: string | null;
  numberOfRooms: string | null;
  numberOfTVs: string | null;
  numberOfVehicles: string | null;
  occupationalGroup: string | null;
  occupation: string | null;
  primaryCookingImplement: string | null;
  relationshipToHouseholdHead: string | null;
  typeOfRoof: string | null;
  typeOfToilet: string | null;
  /**
   * Score in percentage
   */
  questionnaireScore: number | null;
}

export interface UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_profile_personalInformation_coverageDetails_provider {
  __typename: "CoverageHmoProviderInputType";
  id: string | null;
  name: string | null;
}

export interface UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_profile_personalInformation_coverageDetails {
  __typename: "CoverageDetailsInputType";
  id: string | null;
  coverageType: string | null;
  companyName: string | null;
  companyAddress: string | null;
  familyName: string | null;
  familyAddress: string | null;
  name: string | null;
  contactAddress: string | null;
  memberNumber: string | null;
  memberPlan: string | null;
  employeeNumber: string | null;
  capturedDate: any | null;
  capturedBy: string | null;
  enrolledBy: string | null;
  enrollmentDateTime: any | null;
  paymentDateTime: any | null;
  paymentFrequency: string | null;
  memberStartDate: any | null;
  memberDueDate: any | null;
  memberStatus: string | null;
  memberUniqueId: string | null;
  parentMemberUniqueId: string | null;
  primaryProviderId: string | null;
  primaryProviderName: string | null;
  secondaryProviderName: string | null;
  tertiaryProviderName: string | null;
  primaryProviderAddress: string | null;
  secondaryProviderAddress: string | null;
  tertiaryProviderAddress: string | null;
  capitatedMember: string | null;
  capitatedAmount: string | null;
  employeeType: string | null;
  employeeDivision: string | null;
  occupation: string | null;
  retired: boolean | null;
  memberPlanGroup: string | null;
  memberPlanSubGroup: string | null;
  terminationDate: any | null;
  premiumCollected: string | null;
  premiumOutstanding: string | null;
  registrationSource: string | null;
  administrationAgency: string | null;
  commissionPayable: string | null;
  enrollmentAgent: string | null;
  enrollmentAgency: string | null;
  commissionRate: string | null;
  planCategory: string | null;
  salesWing: string | null;
  sponsorType: string | null;
  sponsorName: string | null;
  referrer: string | null;
  referrerCode: string | null;
  referrerNumber: string | null;
  referrerCommissionRate: string | null;
  referrerCommissionPayable: string | null;
  employerCode: string | null;
  activationDatetime: any | null;
  totalPremiumAmountPaid: string | null;
  questionnaireData: UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_profile_personalInformation_coverageDetails_questionnaireData | null;
  provider: UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_profile_personalInformation_coverageDetails_provider | null;
}

export interface UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_profile_personalInformation_secondaryPhoneNumber {
  __typename: "PhoneNumberFields";
  value: string | null;
  countryCode: string | null;
  countryName: string | null;
}

export interface UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_profile_personalInformation {
  __typename: "PersonalInformation";
  id: string | null;
  rank: string | null;
  title: string | null;
  department: string | null;
  speciality: string | null;
  displayPictureUrl: string | null;
  firstName: string | null;
  lastName: string | null;
  middleName: string | null;
  dateOfBirth: any | null;
  bloodGroup: string | null;
  genoType: string | null;
  weight: string | null;
  weightUnit: string | null;
  height: string | null;
  heightUnit: string | null;
  address: string | null;
  gender: Gender | null;
  secondaryEmail: string | null;
  folioNumber: string | null;
  clinicalTrials: string | null;
  patientFileOrCardNo: string | null;
  coverageDetails: UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_profile_personalInformation_coverageDetails[] | null;
  secondaryPhoneNumber: UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_profile_personalInformation_secondaryPhoneNumber | null;
  nin: string | null;
  votersId: string | null;
  passportNumber: string | null;
  bvn: string | null;
  registrationNote: string | null;
  lga: string | null;
  ward: string | null;
  buildingName: string | null;
  buildingLevel: string | null;
  countryOfResidence: string | null;
  stateOfResidence: string | null;
  patientCategory: string[] | null;
  nationality: string | null;
  state: string | null;
  originLga: string | null;
  city: string | null;
  placeOfBirth: string | null;
  userRole: string | null;
}

export interface UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_profile {
  __typename: "ProfileModel";
  id: string;
  clinifyId: string;
  fullName: string;
  personalInformation: UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_profile_personalInformation | null;
}

export interface UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_hmoClaim {
  __typename: "HmoClaimModel";
  id: string;
}

export interface UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_clinicalDiagnosis {
  __typename: "DiagnosisInputType";
  diagnosisICD10: string | null;
  diagnosisICD11: string | null;
  diagnosisSNOMED: string | null;
}

export interface UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  itemId: string | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_referringHospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_additionalNotes {
  __typename: "AdditionalNoteModel";
  id: string;
  additionalNote: string | null;
  conceal: boolean | null;
}

export interface UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_bill {
  __typename: "BillModel";
  id: string;
  billStatus: BillStatus;
  createdDate: any;
}

export interface UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_examinationType {
  __typename: "InvestigationExamTypeInputType";
  ref: string | null;
  priority: string | null;
  examType: string;
  loinc: string | null;
  provider: string | null;
  itemId: string | null;
  preauthorizationDetailsId: string | null;
  indication: string | null;
}

export interface UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_labResult_details_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_labResult_details_testResults {
  __typename: "TestResultsType";
  name: string | null;
  value: string | null;
  unit: string | null;
  dropdown: string[] | null;
  box: boolean | null;
  sensitivityBox: boolean | null;
  units: string[] | null;
  dependsOn: string[] | null;
  range: string | null;
  extraValue: string | null;
  hasExtraValue: boolean | null;
  valueTwo: string | null;
  tabular: string[] | null;
  referenceRange: boolean | null;
}

export interface UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_labResult_details_extraTestResults {
  __typename: "TestResultsType";
  name: string | null;
  value: string | null;
  unit: string | null;
  dropdown: string[] | null;
  box: boolean | null;
  sensitivityBox: boolean | null;
  units: string[] | null;
  dependsOn: string[] | null;
  range: string | null;
}

export interface UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_labResult_details {
  __typename: "LabTestDetailType";
  testDate: any | null;
  duration: string | null;
  serviceDetails: UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_labResult_details_serviceDetails | null;
  specimenCollected: specimenCollection | null;
  specimenTypes: string[] | null;
  specimenNumber: string | null;
  natureSiteOfSpecimen: string | null;
  specimenReceivedDate: any | null;
  collectionDate: any | null;
  collectedBy: string | null;
  performedBy: string | null;
  performedBySignature: string | null;
  performedBySignatureType: string | null;
  performedBySignatureDateTime: any | null;
  verifiedBy: string | null;
  verifiedBySignature: string | null;
  verifiedBySignatureType: string | null;
  verifiedBySignatureDateTime: any | null;
  resultDate: any | null;
  pathologistName: string | null;
  pathologistSignature: string | null;
  pathologistSignatureType: string | null;
  pathologistSignatureDateTime: any | null;
  pathologistReport: string | null;
  conclusion: string | null;
  microscopicExam: string | null;
  grossExam: string | null;
  pertinentHistory: string | null;
  reportDate: any | null;
  finalReportDate: any | null;
  done: boolean | null;
  testName: string | null;
  additionalNote: string | null;
  testResults: UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_labResult_details_testResults[] | null;
  extraTestResults: UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_labResult_details_extraTestResults[] | null;
}

export interface UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_labResult {
  __typename: "LabResultModel";
  id: string;
  documentUrl: string[] | null;
  details: UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_labResult_details[] | null;
  facilityName: string | null;
  additionalNote: string | null;
  concealAdditionalNote: boolean;
  facilityAddress: string | null;
}

export interface UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_radiologyResult_details_serviceDetails {
  __typename: "ServiceDetailInputType";
  priceId: string | null;
  type: string | null;
  name: string | null;
  quantity: string | null;
  pricePerUnit: number | null;
  patientType: string | null;
  paymentType: string | null;
  reference: string | null;
}

export interface UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_radiologyResult_details {
  __typename: "RadiologyExamDetailType";
  examinationDate: any | null;
  duration: string | null;
  patientType: PatientType | null;
  paymentType: string | null;
  serviceDetails: UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_radiologyResult_details_serviceDetails | null;
  radiographerName: string | null;
  radiographerSignature: string | null;
  radiographerSignatureType: string | null;
  radiographerSignatureDateTime: any | null;
  examinationNumber: string | null;
  indication: string | null;
  comparison: string | null;
  technique: string | null;
  radiographerReport: string | null;
  impression: string | null;
  done: boolean | null;
  examType: string | null;
  contrastConfirmed: boolean | null;
  radiologistName: string | null;
  radiologistSignature: string | null;
  radiologistSignatureType: string | null;
  radiologistSignatureDateTime: any | null;
  verifiedBy: string | null;
  verifiedBySignature: string | null;
  verifiedBySignatureType: string | null;
  verifiedBySignatureDateTime: any | null;
}

export interface UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_radiologyResult {
  __typename: "RadiologyResultModel";
  id: string;
  documentUrl: string[] | null;
  details: UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_radiologyResult_details[] | null;
  facilityName: string | null;
  facilityAddress: string | null;
}

export interface UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_preauthorizationDetails_provider {
  __typename: "HmoProviderModel";
  id: string;
  name: string;
  providerCode: string;
}

export interface UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_preauthorizationDetails {
  __typename: "PreauthorizationDetailsModel";
  id: string;
  recordType: BillableRecords;
  ref: string | null;
  paCode: string | null;
  paStatus: string | null;
  admissionId: string | null;
  antenatalDetailsId: string | null;
  consultationId: string | null;
  immunizationDetailId: string | null;
  investigationId: string | null;
  medicationDetailsId: string | null;
  surgeryId: string | null;
  registrationProfileId: string | null;
  nursingServiceId: string | null;
  postnatalId: string | null;
  labourDeliveryId: string | null;
  oncologyConsultationHistoryId: string | null;
  hospitalId: string;
  lastModifierId: string | null;
  lastModifierName: string | null;
  updatedDate: any | null;
  providerId: string | null;
  provider: UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_preauthorizationDetails_provider | null;
}

export interface UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult {
  __typename: "InvestigationModel";
  id: string;
  requestType: string;
  requestDate: any | null;
  clinifyId: string | null;
  testInfo: UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_testInfo[] | null;
  profileId: string | null;
  profile: UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_profile | null;
  priority: string | null;
  patientType: string | null;
  hmoProviderId: string | null;
  hmoClaim: UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_hmoClaim | null;
  clinicalDiagnosis: UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_clinicalDiagnosis[] | null;
  serviceDetails: UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_serviceDetails[] | null;
  isPackage: boolean;
  orderedBy: string | null;
  specialty: string | null;
  rank: string | null;
  department: string | null;
  facilityName: string | null;
  facilityAddress: string | null;
  external: boolean;
  referringHospital: UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_referringHospital | null;
  additionalNotes: UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_additionalNotes[] | null;
  bill: UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_bill | null;
  examinationType: UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_examinationType[] | null;
  clinicalHistory: string | null;
  status: investigationStatus | null;
  isRequested: boolean | null;
  documentUrl: string[] | null;
  radiologyContrastConfirmation: boolean | null;
  hospitalId: string | null;
  hospital: UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_hospital | null;
  labResult: UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_labResult | null;
  radiologyResult: UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_radiologyResult | null;
  preauthorizationDetails: UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult_preauthorizationDetails[] | null;
  billStatus: string | null;
}

export interface UpdateInvestigationWithRadiologyResult {
  updateInvestigationWithRadiologyResult: UpdateInvestigationWithRadiologyResult_updateInvestigationWithRadiologyResult;
}

export interface UpdateInvestigationWithRadiologyResultVariables {
  id: string;
  input: InvestigationWithRadiologyInput;
  pin?: string | null;
}
