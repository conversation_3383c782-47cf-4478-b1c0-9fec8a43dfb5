/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { FundTransactionStatus, PayoutCommissionPayer } from "./globalTypes";

// ====================================================
// GraphQL subscription operation: HospitalCapitationDetailsUpdatedSubs
// ====================================================

export interface HospitalCapitationDetailsUpdatedSubs_HospitalCapitationDetailsUpdated_createdBy {
  __typename: "ProfileModel";
  id: string;
  fullName: string;
  type: string;
  clinifyId: string;
  title: string | null;
}

export interface HospitalCapitationDetailsUpdatedSubs_HospitalCapitationDetailsUpdated_hospital {
  __typename: "HospitalModel";
  id: string;
  name: string | null;
  address: string | null;
}

export interface HospitalCapitationDetailsUpdatedSubs_HospitalCapitationDetailsUpdated_auditApproval {
  __typename: "ClaimsAccountApprovalType";
  createdDate: any;
  creatorName: string;
  approvalGroup: string;
  creatorId: string;
}

export interface HospitalCapitationDetailsUpdatedSubs_HospitalCapitationDetailsUpdated_hmoPlanType {
  __typename: "HmoPlanTypeModel";
  id: string;
  name: string | null;
}

export interface HospitalCapitationDetailsUpdatedSubs_HospitalCapitationDetailsUpdated_detailsByPlanType {
  __typename: "CapitationDetailByPlanType";
  enrolleeCount: number;
  planType: string;
  planTypeName: string;
  totalCapitationAmount: number;
  payoutDecreasePercentage: number | null;
}

export interface HospitalCapitationDetailsUpdatedSubs_HospitalCapitationDetailsUpdated {
  __typename: "TransferFundModel";
  id: string;
  createdBy: HospitalCapitationDetailsUpdatedSubs_HospitalCapitationDetailsUpdated_createdBy | null;
  amount: number;
  createdDate: any;
  updatedDate: any;
  destinationAccountName: string | null;
  destinationAccountNumber: string | null;
  destinationBankCode: string | null;
  destinationBankName: string | null;
  narration: string | null;
  originatorName: string | null;
  sourceAccountNumber: string | null;
  transferReference: string | null;
  transferStatus: FundTransactionStatus;
  additionalNote: string | null;
  serviceChargeAmount: number | null;
  hospitalId: string | null;
  hmoPlanTypeId: string | null;
  hospital: HospitalCapitationDetailsUpdatedSubs_HospitalCapitationDetailsUpdated_hospital | null;
  enrolleeCount: number | null;
  isEnrolleePayout: boolean | null;
  totalCapitationAmount: number | null;
  auditApproval: HospitalCapitationDetailsUpdatedSubs_HospitalCapitationDetailsUpdated_auditApproval[] | null;
  hmoPlanType: HospitalCapitationDetailsUpdatedSubs_HospitalCapitationDetailsUpdated_hmoPlanType | null;
  detailsByPlanType: HospitalCapitationDetailsUpdatedSubs_HospitalCapitationDetailsUpdated_detailsByPlanType[] | null;
  payoutCommissionPayer: PayoutCommissionPayer | null;
  payoutDecreasePercentage: number | null;
}

export interface HospitalCapitationDetailsUpdatedSubs {
  HospitalCapitationDetailsUpdated: HospitalCapitationDetailsUpdatedSubs_HospitalCapitationDetailsUpdated;
}

export interface HospitalCapitationDetailsUpdatedSubsVariables {
  hmoProviderId: string;
}
