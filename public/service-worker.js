!function(e){var t={};function s(n){if(t[n])return t[n].exports;var r=t[n]={i:n,l:!1,exports:{}};return e[n].call(r.exports,r,r.exports,s),r.l=!0,r.exports}s.m=e,s.c=t,s.d=function(e,t,n){s.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},s.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},s.t=function(e,t){if(1&t&&(e=s(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(s.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)s.d(n,r,function(t){return e[t]}.bind(null,r));return n},s.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return s.d(t,"a",t),t},s.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},s.p="",s(s.s=14)}([function(e,t,s){"use strict";try{self["workbox:precaching:6.1.5"]&&_()}catch(e){}},function(e,t,s){"use strict";try{self["workbox:core:6.4.1"]&&_()}catch(e){}},function(e,t,s){"use strict";try{self["workbox:core:6.4.1"]&&_()}catch(e){}},function(e,t,s){"use strict";try{self["workbox:routing:6.1.5"]&&_()}catch(e){}},function(e,t,s){"use strict";try{self["workbox:core:6.4.1"]&&_()}catch(e){}},function(e,t,s){"use strict";try{self["workbox:strategies:6.1.5"]&&_()}catch(e){}},function(e,t,s){"use strict";try{self["workbox:routing:6.4.1"]&&_()}catch(e){}},function(e,t,s){"use strict";try{self["workbox:core:6.4.1"]&&_()}catch(e){}},function(e,t,s){"use strict";try{self["workbox:expiration:6.1.5"]&&_()}catch(e){}},function(e,t,s){var n;e.exports=(n=s(12),function(e){var t=n,s=t.lib,r=s.WordArray,i=s.Hasher,a=t.algo,o=[];!function(){for(var t=0;t<64;t++)o[t]=***********e.abs(e.sin(t+1))|0}();var c=a.MD5=i.extend({_doReset:function(){this._hash=new r.init([**********,**********,**********,*********])},_doProcessBlock:function(e,t){for(var s=0;s<16;s++){var n=t+s,r=e[n];e[n]=********&(r<<8|r>>>24)|**********&(r<<24|r>>>8)}var i=this._hash.words,a=e[t+0],c=e[t+1],d=e[t+2],p=e[t+3],g=e[t+4],w=e[t+5],y=e[t+6],m=e[t+7],_=e[t+8],v=e[t+9],b=e[t+10],x=e[t+11],R=e[t+12],C=e[t+13],q=e[t+14],k=e[t+15],U=i[0],E=i[1],S=i[2],O=i[3];U=h(U,E,S,O,a,7,o[0]),O=h(O,U,E,S,c,12,o[1]),S=h(S,O,U,E,d,17,o[2]),E=h(E,S,O,U,p,22,o[3]),U=h(U,E,S,O,g,7,o[4]),O=h(O,U,E,S,w,12,o[5]),S=h(S,O,U,E,y,17,o[6]),E=h(E,S,O,U,m,22,o[7]),U=h(U,E,S,O,_,7,o[8]),O=h(O,U,E,S,v,12,o[9]),S=h(S,O,U,E,b,17,o[10]),E=h(E,S,O,U,x,22,o[11]),U=h(U,E,S,O,R,7,o[12]),O=h(O,U,E,S,C,12,o[13]),S=h(S,O,U,E,q,17,o[14]),U=l(U,E=h(E,S,O,U,k,22,o[15]),S,O,c,5,o[16]),O=l(O,U,E,S,y,9,o[17]),S=l(S,O,U,E,x,14,o[18]),E=l(E,S,O,U,a,20,o[19]),U=l(U,E,S,O,w,5,o[20]),O=l(O,U,E,S,b,9,o[21]),S=l(S,O,U,E,k,14,o[22]),E=l(E,S,O,U,g,20,o[23]),U=l(U,E,S,O,v,5,o[24]),O=l(O,U,E,S,q,9,o[25]),S=l(S,O,U,E,p,14,o[26]),E=l(E,S,O,U,_,20,o[27]),U=l(U,E,S,O,C,5,o[28]),O=l(O,U,E,S,d,9,o[29]),S=l(S,O,U,E,m,14,o[30]),U=u(U,E=l(E,S,O,U,R,20,o[31]),S,O,w,4,o[32]),O=u(O,U,E,S,_,11,o[33]),S=u(S,O,U,E,x,16,o[34]),E=u(E,S,O,U,q,23,o[35]),U=u(U,E,S,O,c,4,o[36]),O=u(O,U,E,S,g,11,o[37]),S=u(S,O,U,E,m,16,o[38]),E=u(E,S,O,U,b,23,o[39]),U=u(U,E,S,O,C,4,o[40]),O=u(O,U,E,S,a,11,o[41]),S=u(S,O,U,E,p,16,o[42]),E=u(E,S,O,U,y,23,o[43]),U=u(U,E,S,O,v,4,o[44]),O=u(O,U,E,S,R,11,o[45]),S=u(S,O,U,E,k,16,o[46]),U=f(U,E=u(E,S,O,U,d,23,o[47]),S,O,a,6,o[48]),O=f(O,U,E,S,m,10,o[49]),S=f(S,O,U,E,q,15,o[50]),E=f(E,S,O,U,w,21,o[51]),U=f(U,E,S,O,R,6,o[52]),O=f(O,U,E,S,p,10,o[53]),S=f(S,O,U,E,b,15,o[54]),E=f(E,S,O,U,c,21,o[55]),U=f(U,E,S,O,_,6,o[56]),O=f(O,U,E,S,k,10,o[57]),S=f(S,O,U,E,y,15,o[58]),E=f(E,S,O,U,C,21,o[59]),U=f(U,E,S,O,g,6,o[60]),O=f(O,U,E,S,x,10,o[61]),S=f(S,O,U,E,d,15,o[62]),E=f(E,S,O,U,v,21,o[63]),i[0]=i[0]+U|0,i[1]=i[1]+E|0,i[2]=i[2]+S|0,i[3]=i[3]+O|0},_doFinalize:function(){var t=this._data,s=t.words,n=8*this._nDataBytes,r=8*t.sigBytes;s[r>>>5]|=128<<24-r%32;var i=e.floor(n/**********),a=n;s[15+(r+64>>>9<<4)]=********&(i<<8|i>>>24)|**********&(i<<24|i>>>8),s[14+(r+64>>>9<<4)]=********&(a<<8|a>>>24)|**********&(a<<24|a>>>8),t.sigBytes=4*(s.length+1),this._process();for(var o=this._hash,c=o.words,h=0;h<4;h++){var l=c[h];c[h]=********&(l<<8|l>>>24)|**********&(l<<24|l>>>8)}return o},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});function h(e,t,s,n,r,i,a){var o=e+(t&s|~t&n)+r+a;return(o<<i|o>>>32-i)+t}function l(e,t,s,n,r,i,a){var o=e+(t&n|s&~n)+r+a;return(o<<i|o>>>32-i)+t}function u(e,t,s,n,r,i,a){var o=e+(t^s^n)+r+a;return(o<<i|o>>>32-i)+t}function f(e,t,s,n,r,i,a){var o=e+(s^(t|~n))+r+a;return(o<<i|o>>>32-i)+t}t.MD5=i._createHelper(c),t.HmacMD5=i._createHmacHelper(c)}(Math),n.MD5)},function(e,t,s){"use strict";try{self["workbox:core:5.1.4"]&&_()}catch(e){}},function(e,t,s){"use strict";try{self["workbox:strategies:6.4.1"]&&_()}catch(e){}},function(e,t,s){var n;e.exports=(n=n||function(e,t){var n;if("undefined"!=typeof window&&window.crypto&&(n=window.crypto),!n&&"undefined"!=typeof window&&window.msCrypto&&(n=window.msCrypto),!n&&"undefined"!=typeof global&&global.crypto&&(n=global.crypto),!n)try{n=s(13)}catch(e){}var r=function(){if(n){if("function"==typeof n.getRandomValues)try{return n.getRandomValues(new Uint32Array(1))[0]}catch(e){}if("function"==typeof n.randomBytes)try{return n.randomBytes(4).readInt32LE()}catch(e){}}throw new Error("Native crypto module could not be used to get secure random number.")},i=Object.create||function(){function e(){}return function(t){var s;return e.prototype=t,s=new e,e.prototype=null,s}}(),a={},o=a.lib={},c=o.Base={extend:function(e){var t=i(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},h=o.WordArray=c.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:4*e.length},toString:function(e){return(e||u).stringify(this)},concat:function(e){var t=this.words,s=e.words,n=this.sigBytes,r=e.sigBytes;if(this.clamp(),n%4)for(var i=0;i<r;i++){var a=s[i>>>2]>>>24-i%4*8&255;t[n+i>>>2]|=a<<24-(n+i)%4*8}else for(i=0;i<r;i+=4)t[n+i>>>2]=s[i>>>2];return this.sigBytes+=r,this},clamp:function(){var t=this.words,s=this.sigBytes;t[s>>>2]&=4294967295<<32-s%4*8,t.length=e.ceil(s/4)},clone:function(){var e=c.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t=[],s=0;s<e;s+=4)t.push(r());return new h.init(t,e)}}),l=a.enc={},u=l.Hex={stringify:function(e){for(var t=e.words,s=e.sigBytes,n=[],r=0;r<s;r++){var i=t[r>>>2]>>>24-r%4*8&255;n.push((i>>>4).toString(16)),n.push((15&i).toString(16))}return n.join("")},parse:function(e){for(var t=e.length,s=[],n=0;n<t;n+=2)s[n>>>3]|=parseInt(e.substr(n,2),16)<<24-n%8*4;return new h.init(s,t/2)}},f=l.Latin1={stringify:function(e){for(var t=e.words,s=e.sigBytes,n=[],r=0;r<s;r++){var i=t[r>>>2]>>>24-r%4*8&255;n.push(String.fromCharCode(i))}return n.join("")},parse:function(e){for(var t=e.length,s=[],n=0;n<t;n++)s[n>>>2]|=(255&e.charCodeAt(n))<<24-n%4*8;return new h.init(s,t)}},d=l.Utf8={stringify:function(e){try{return decodeURIComponent(escape(f.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return f.parse(unescape(encodeURIComponent(e)))}},p=o.BufferedBlockAlgorithm=c.extend({reset:function(){this._data=new h.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=d.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var s,n=this._data,r=n.words,i=n.sigBytes,a=this.blockSize,o=i/(4*a),c=(o=t?e.ceil(o):e.max((0|o)-this._minBufferSize,0))*a,l=e.min(4*c,i);if(c){for(var u=0;u<c;u+=a)this._doProcessBlock(r,u);s=r.splice(0,c),n.sigBytes-=l}return new h.init(s,l)},clone:function(){var e=c.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0}),g=(o.Hasher=p.extend({cfg:c.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){p.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,s){return new e.init(s).finalize(t)}},_createHmacHelper:function(e){return function(t,s){return new g.HMAC.init(e,s).finalize(t)}}}),a.algo={});return a}(Math),n)},function(e,t){e.exports=require("crypto")},function(e,t,s){"use strict";s.r(t);var n=s(9),r=s.n(n);function i(e){return new Promise((t,s)=>{e.oncomplete=e.onsuccess=()=>t(e.result),e.onabort=e.onerror=()=>s(e.error)})}function a(e,t){const s=indexedDB.open(e);s.onupgradeneeded=()=>s.result.createObjectStore(t);const n=i(s);return(e,s)=>n.then(n=>s(n.transaction(t,e).objectStore(t)))}let o;function c(){return o||(o=a("keyval-store","keyval")),o}s(4);const h=(e,...t)=>{let s=e;return t.length>0&&(s+=" :: "+JSON.stringify(t)),s};class l extends Error{constructor(e,t){super(h(e,t)),this.name=e,this.details=t}}function u(e){e.then(()=>{})}s(10);class f{constructor(e,t,{onupgradeneeded:s,onversionchange:n}={}){this._db=null,this._name=e,this._version=t,this._onupgradeneeded=s,this._onversionchange=n||(()=>this.close())}get db(){return this._db}async open(){if(!this._db)return this._db=await new Promise((e,t)=>{let s=!1;setTimeout(()=>{s=!0,t(new Error("The open request was blocked and timed out"))},this.OPEN_TIMEOUT);const n=indexedDB.open(this._name,this._version);n.onerror=()=>t(n.error),n.onupgradeneeded=e=>{s?(n.transaction.abort(),n.result.close()):"function"==typeof this._onupgradeneeded&&this._onupgradeneeded(e)},n.onsuccess=()=>{const t=n.result;s?t.close():(t.onversionchange=this._onversionchange.bind(this),e(t))}}),this}async getKey(e,t){return(await this.getAllKeys(e,t,1))[0]}async getAll(e,t,s){return await this.getAllMatching(e,{query:t,count:s})}async getAllKeys(e,t,s){return(await this.getAllMatching(e,{query:t,count:s,includeKeys:!0})).map(e=>e.key)}async getAllMatching(e,{index:t,query:s=null,direction:n="next",count:r,includeKeys:i=!1}={}){return await this.transaction([e],"readonly",(a,o)=>{const c=a.objectStore(e),h=t?c.index(t):c,l=[],u=h.openCursor(s,n);u.onsuccess=()=>{const e=u.result;e?(l.push(i?e:e.value),r&&l.length>=r?o(l):e.continue()):o(l)}})}async transaction(e,t,s){return await this.open(),await new Promise((n,r)=>{const i=this._db.transaction(e,t);i.onabort=()=>r(i.error),i.oncomplete=()=>n(),s(i,e=>n(e))})}async _call(e,t,s,...n){return await this.transaction([t],s,(s,r)=>{const i=s.objectStore(t),a=i[e].apply(i,n);a.onsuccess=()=>r(a.result)})}close(){this._db&&(this._db.close(),this._db=null)}}f.prototype.OPEN_TIMEOUT=2e3;const d={readonly:["get","count","getKey","getAll","getAllKeys"],readwrite:["add","put","clear","delete"]};for(const[e,t]of Object.entries(d))for(const s of t)s in IDBObjectStore.prototype&&(f.prototype[s]=async function(t,...n){return await this._call(s,t,e,...n)});s(8);const p=e=>{const t=new URL(e,location.href);return t.hash="",t.href};class g{constructor(e){this._cacheName=e,this._db=new f("workbox-expiration",1,{onupgradeneeded:e=>this._handleUpgrade(e)})}_handleUpgrade(e){const t=e.target.result.createObjectStore("cache-entries",{keyPath:"id"});t.createIndex("cacheName","cacheName",{unique:!1}),t.createIndex("timestamp","timestamp",{unique:!1}),(async e=>{await new Promise((t,s)=>{const n=indexedDB.deleteDatabase(e);n.onerror=()=>{s(n.error)},n.onblocked=()=>{s(new Error("Delete blocked"))},n.onsuccess=()=>{t()}})})(this._cacheName)}async setTimestamp(e,t){const s={url:e=p(e),timestamp:t,cacheName:this._cacheName,id:this._getId(e)};await this._db.put("cache-entries",s)}async getTimestamp(e){return(await this._db.get("cache-entries",this._getId(e))).timestamp}async expireEntries(e,t){const s=await this._db.transaction("cache-entries","readwrite",(s,n)=>{const r=s.objectStore("cache-entries").index("timestamp").openCursor(null,"prev"),i=[];let a=0;r.onsuccess=()=>{const s=r.result;if(s){const n=s.value;n.cacheName===this._cacheName&&(e&&n.timestamp<e||t&&a>=t?i.push(s.value):a++),s.continue()}else n(i)}}),n=[];for(const e of s)await this._db.delete("cache-entries",e.id),n.push(e.url);return n}_getId(e){return this._cacheName+"|"+p(e)}}class w{constructor(e,t={}){this._isRunning=!1,this._rerunRequested=!1,this._maxEntries=t.maxEntries,this._maxAgeSeconds=t.maxAgeSeconds,this._matchOptions=t.matchOptions,this._cacheName=e,this._timestampModel=new g(e)}async expireEntries(){if(this._isRunning)return void(this._rerunRequested=!0);this._isRunning=!0;const e=this._maxAgeSeconds?Date.now()-1e3*this._maxAgeSeconds:0,t=await this._timestampModel.expireEntries(e,this._maxEntries),s=await self.caches.open(this._cacheName);for(const e of t)await s.delete(e,this._matchOptions);this._isRunning=!1,this._rerunRequested&&(this._rerunRequested=!1,u(this.expireEntries()))}async updateTimestamp(e){await this._timestampModel.setTimestamp(e,Date.now())}async isURLExpired(e){if(this._maxAgeSeconds){return await this._timestampModel.getTimestamp(e)<Date.now()-1e3*this._maxAgeSeconds}return!1}async delete(){this._rerunRequested=!1,await this._timestampModel.expireEntries(1/0)}}const y={googleAnalytics:"googleAnalytics",precache:"precache-v2",prefix:"workbox",runtime:"runtime",suffix:"undefined"!=typeof registration?registration.scope:""},m=e=>[y.prefix,e,y.suffix].filter(e=>e&&e.length>0).join("-"),_=e=>e||m(y.runtime),v=new Set;s(1);const b=(e,...t)=>{let s=e;return t.length>0&&(s+=" :: "+JSON.stringify(t)),s};class x extends Error{constructor(e,t){super(b(e,t)),this.name=e,this.details=t}}const R={googleAnalytics:"googleAnalytics",precache:"precache-v2",prefix:"workbox",runtime:"runtime",suffix:"undefined"!=typeof registration?registration.scope:""},C=e=>[R.prefix,e,R.suffix].filter(e=>e&&e.length>0).join("-"),q=e=>e||C(R.precache),k=e=>e||C(R.runtime);function U(e,t){const s=t();return e.waitUntil(s),s}s(0);function E(e){if(!e)throw new x("add-to-cache-list-unexpected-type",{entry:e});if("string"==typeof e){const t=new URL(e,location.href);return{cacheKey:t.href,url:t.href}}const{revision:t,url:s}=e;if(!s)throw new x("add-to-cache-list-unexpected-type",{entry:e});if(!t){const e=new URL(s,location.href);return{cacheKey:e.href,url:e.href}}const n=new URL(s,location.href),r=new URL(s,location.href);return n.searchParams.set("__WB_REVISION__",t),{cacheKey:n.href,url:r.href}}class S{constructor(){this.updatedURLs=[],this.notUpdatedURLs=[],this.handlerWillStart=async({request:e,state:t})=>{t&&(t.originalRequest=e)},this.cachedResponseWillBeUsed=async({event:e,state:t,cachedResponse:s})=>{if("install"===e.type){const e=t.originalRequest.url;s?this.notUpdatedURLs.push(e):this.updatedURLs.push(e)}return s}}}class O{constructor({precacheController:e}){this.cacheKeyWillBeUsed=async({request:e,params:t})=>{const s=t&&t.cacheKey||this._precacheController.getCacheKeyForURL(e.url);return s?new Request(s):e},this._precacheController=e}}let T;async function L(e,t){let s=null;if(e.url){s=new URL(e.url).origin}if(s!==self.location.origin)throw new x("cross-origin-copy-response",{origin:s});const n=e.clone(),r={headers:new Headers(n.headers),status:n.status,statusText:n.statusText},i=t?t(r):r,a=function(){if(void 0===T){const e=new Response("");if("body"in e)try{new Response(e.body),T=!0}catch(e){T=!1}T=!1}return T}()?n.body:await n.blob();return new Response(a,i)}function N(e,t){const s=new URL(e);for(const e of t)s.searchParams.delete(e);return s.href}class P{constructor(){this.promise=new Promise((e,t)=>{this.resolve=e,this.reject=t})}}const M=new Set;s(11);function K(e){return"string"==typeof e?new Request(e):e}class A{constructor(e,t){this._cacheKeys={},Object.assign(this,t),this.event=t.event,this._strategy=e,this._handlerDeferred=new P,this._extendLifetimePromises=[],this._plugins=[...e.plugins],this._pluginStateMap=new Map;for(const e of this._plugins)this._pluginStateMap.set(e,{});this.event.waitUntil(this._handlerDeferred.promise)}async fetch(e){const{event:t}=this;let s=K(e);if("navigate"===s.mode&&t instanceof FetchEvent&&t.preloadResponse){const e=await t.preloadResponse;if(e)return e}const n=this.hasCallback("fetchDidFail")?s.clone():null;try{for(const e of this.iterateCallbacks("requestWillFetch"))s=await e({request:s.clone(),event:t})}catch(e){if(e instanceof Error)throw new x("plugin-error-request-will-fetch",{thrownErrorMessage:e.message})}const r=s.clone();try{let e;e=await fetch(s,"navigate"===s.mode?void 0:this._strategy.fetchOptions);for(const s of this.iterateCallbacks("fetchDidSucceed"))e=await s({event:t,request:r,response:e});return e}catch(e){throw n&&await this.runCallbacks("fetchDidFail",{error:e,event:t,originalRequest:n.clone(),request:r.clone()}),e}}async fetchAndCachePut(e){const t=await this.fetch(e),s=t.clone();return this.waitUntil(this.cachePut(e,s)),t}async cacheMatch(e){const t=K(e);let s;const{cacheName:n,matchOptions:r}=this._strategy,i=await this.getCacheKey(t,"read"),a=Object.assign(Object.assign({},r),{cacheName:n});s=await caches.match(i,a);for(const e of this.iterateCallbacks("cachedResponseWillBeUsed"))s=await e({cacheName:n,matchOptions:r,cachedResponse:s,request:i,event:this.event})||void 0;return s}async cachePut(e,t){const s=K(e);var n;await(n=0,new Promise(e=>setTimeout(e,n)));const r=await this.getCacheKey(s,"write");if(!t)throw new x("cache-put-with-no-response",{url:(i=r.url,new URL(String(i),location.href).href.replace(new RegExp("^"+location.origin),""))});var i;const a=await this._ensureResponseSafeToCache(t);if(!a)return!1;const{cacheName:o,matchOptions:c}=this._strategy,h=await self.caches.open(o),l=this.hasCallback("cacheDidUpdate"),u=l?await async function(e,t,s,n){const r=N(t.url,s);if(t.url===r)return e.match(t,n);const i=Object.assign(Object.assign({},n),{ignoreSearch:!0}),a=await e.keys(t,i);for(const t of a){if(r===N(t.url,s))return e.match(t,n)}}(h,r.clone(),["__WB_REVISION__"],c):null;try{await h.put(r,l?a.clone():a)}catch(e){if(e instanceof Error)throw"QuotaExceededError"===e.name&&await async function(){for(const e of M)await e()}(),e}for(const e of this.iterateCallbacks("cacheDidUpdate"))await e({cacheName:o,oldResponse:u,newResponse:a.clone(),request:r,event:this.event});return!0}async getCacheKey(e,t){const s=`${e.url} | ${t}`;if(!this._cacheKeys[s]){let n=e;for(const e of this.iterateCallbacks("cacheKeyWillBeUsed"))n=K(await e({mode:t,request:n,event:this.event,params:this.params}));this._cacheKeys[s]=n}return this._cacheKeys[s]}hasCallback(e){for(const t of this._strategy.plugins)if(e in t)return!0;return!1}async runCallbacks(e,t){for(const s of this.iterateCallbacks(e))await s(t)}*iterateCallbacks(e){for(const t of this._strategy.plugins)if("function"==typeof t[e]){const s=this._pluginStateMap.get(t),n=n=>{const r=Object.assign(Object.assign({},n),{state:s});return t[e](r)};yield n}}waitUntil(e){return this._extendLifetimePromises.push(e),e}async doneWaiting(){let e;for(;e=this._extendLifetimePromises.shift();)await e}destroy(){this._handlerDeferred.resolve(null)}async _ensureResponseSafeToCache(e){let t=e,s=!1;for(const e of this.iterateCallbacks("cacheWillUpdate"))if(t=await e({request:this.request,response:t,event:this.event})||void 0,s=!0,!t)break;return s||t&&200!==t.status&&(t=void 0),t}}class j extends class{constructor(e={}){this.cacheName=k(e.cacheName),this.plugins=e.plugins||[],this.fetchOptions=e.fetchOptions,this.matchOptions=e.matchOptions}handle(e){const[t]=this.handleAll(e);return t}handleAll(e){e instanceof FetchEvent&&(e={event:e,request:e.request});const t=e.event,s="string"==typeof e.request?new Request(e.request):e.request,n="params"in e?e.params:void 0,r=new A(this,{event:t,request:s,params:n}),i=this._getResponse(r,s,t);return[i,this._awaitComplete(i,r,s,t)]}async _getResponse(e,t,s){await e.runCallbacks("handlerWillStart",{event:s,request:t});let n=void 0;try{if(n=await this._handle(t,e),!n||"error"===n.type)throw new x("no-response",{url:t.url})}catch(r){if(r instanceof Error)for(const i of e.iterateCallbacks("handlerDidError"))if(n=await i({error:r,event:s,request:t}),n)break;if(!n)throw r}for(const r of e.iterateCallbacks("handlerWillRespond"))n=await r({event:s,request:t,response:n});return n}async _awaitComplete(e,t,s,n){let r,i;try{r=await e}catch(i){}try{await t.runCallbacks("handlerDidRespond",{event:n,request:s,response:r}),await t.doneWaiting()}catch(e){e instanceof Error&&(i=e)}if(await t.runCallbacks("handlerDidComplete",{event:n,request:s,response:r,error:i}),t.destroy(),i)throw i}}{constructor(e={}){e.cacheName=q(e.cacheName),super(e),this._fallbackToNetwork=!1!==e.fallbackToNetwork,this.plugins.push(j.copyRedirectedCacheableResponsesPlugin)}async _handle(e,t){const s=await t.cacheMatch(e);return s||(t.event&&"install"===t.event.type?await this._handleInstall(e,t):await this._handleFetch(e,t))}async _handleFetch(e,t){let s;if(!this._fallbackToNetwork)throw new x("missing-precache-entry",{cacheName:this.cacheName,url:e.url});return s=await t.fetch(e),s}async _handleInstall(e,t){this._useDefaultCacheabilityPluginIfNeeded();const s=await t.fetch(e);if(!await t.cachePut(e,s.clone()))throw new x("bad-precaching-response",{url:e.url,status:s.status});return s}_useDefaultCacheabilityPluginIfNeeded(){let e=null,t=0;for(const[s,n]of this.plugins.entries())n!==j.copyRedirectedCacheableResponsesPlugin&&(n===j.defaultPrecacheCacheabilityPlugin&&(e=s),n.cacheWillUpdate&&t++);0===t?this.plugins.push(j.defaultPrecacheCacheabilityPlugin):t>1&&null!==e&&this.plugins.splice(e,1)}}j.defaultPrecacheCacheabilityPlugin={cacheWillUpdate:async({response:e})=>!e||e.status>=400?null:e},j.copyRedirectedCacheableResponsesPlugin={cacheWillUpdate:async({response:e})=>e.redirected?await L(e):e};class D{constructor({cacheName:e,plugins:t=[],fallbackToNetwork:s=!0}={}){this._urlsToCacheKeys=new Map,this._urlsToCacheModes=new Map,this._cacheKeysToIntegrities=new Map,this._strategy=new j({cacheName:q(e),plugins:[...t,new O({precacheController:this})],fallbackToNetwork:s}),this.install=this.install.bind(this),this.activate=this.activate.bind(this)}get strategy(){return this._strategy}precache(e){this.addToCacheList(e),this._installAndActiveListenersAdded||(self.addEventListener("install",this.install),self.addEventListener("activate",this.activate),this._installAndActiveListenersAdded=!0)}addToCacheList(e){const t=[];for(const s of e){"string"==typeof s?t.push(s):s&&void 0===s.revision&&t.push(s.url);const{cacheKey:e,url:n}=E(s),r="string"!=typeof s&&s.revision?"reload":"default";if(this._urlsToCacheKeys.has(n)&&this._urlsToCacheKeys.get(n)!==e)throw new x("add-to-cache-list-conflicting-entries",{firstEntry:this._urlsToCacheKeys.get(n),secondEntry:e});if("string"!=typeof s&&s.integrity){if(this._cacheKeysToIntegrities.has(e)&&this._cacheKeysToIntegrities.get(e)!==s.integrity)throw new x("add-to-cache-list-conflicting-integrities",{url:n});this._cacheKeysToIntegrities.set(e,s.integrity)}if(this._urlsToCacheKeys.set(n,e),this._urlsToCacheModes.set(n,r),t.length>0){const e=`Workbox is precaching URLs without revision info: ${t.join(", ")}\nThis is generally NOT safe. Learn more at https://bit.ly/wb-precache`;console.warn(e)}}}install(e){return U(e,async()=>{const t=new S;this.strategy.plugins.push(t);for(const[t,s]of this._urlsToCacheKeys){const n=this._cacheKeysToIntegrities.get(s),r=this._urlsToCacheModes.get(t),i=new Request(t,{integrity:n,cache:r,credentials:"same-origin"});await Promise.all(this.strategy.handleAll({params:{cacheKey:s},request:i,event:e}))}const{updatedURLs:s,notUpdatedURLs:n}=t;return{updatedURLs:s,notUpdatedURLs:n}})}activate(e){return U(e,async()=>{const e=await self.caches.open(this.strategy.cacheName),t=await e.keys(),s=new Set(this._urlsToCacheKeys.values()),n=[];for(const r of t)s.has(r.url)||(await e.delete(r),n.push(r.url));return{deletedURLs:n}})}getURLsToCacheKeys(){return this._urlsToCacheKeys}getCachedURLs(){return[...this._urlsToCacheKeys.keys()]}getCacheKeyForURL(e){const t=new URL(e,location.href);return this._urlsToCacheKeys.get(t.href)}async matchPrecache(e){const t=e instanceof Request?e.url:e,s=this.getCacheKeyForURL(t);if(s){return(await self.caches.open(this.strategy.cacheName)).match(s)}}createHandlerBoundToURL(e){const t=this.getCacheKeyForURL(e);if(!t)throw new x("non-precached-url",{url:e});return s=>(s.request=new Request(e),s.params={cacheKey:t,...s.params},this.strategy.handle(s))}}s(6);s(7);const B=(e,...t)=>{let s=e;return t.length>0&&(s+=" :: "+JSON.stringify(t)),s};class W extends Error{constructor(e,t){super(B(e,t)),this.name=e,this.details=t}}s(3);const H=e=>e&&"object"==typeof e?e:{handle:e};class I{constructor(e,t,s="GET"){this.handler=H(t),this.match=e,this.method=s}setCatchHandler(e){this.catchHandler=H(e)}}class F extends I{constructor(e,t,s){super(({url:t})=>{const s=e.exec(t.href);if(s&&(t.origin===location.origin||0===s.index))return s.slice(1)},t,s)}}class J{constructor(){this._routes=new Map,this._defaultHandlerMap=new Map}get routes(){return this._routes}addFetchListener(){self.addEventListener("fetch",e=>{const{request:t}=e,s=this.handleRequest({request:t,event:e});s&&e.respondWith(s)})}addCacheListener(){self.addEventListener("message",e=>{if(e.data&&"CACHE_URLS"===e.data.type){const{payload:t}=e.data;0;const s=Promise.all(t.urlsToCache.map(t=>{"string"==typeof t&&(t=[t]);const s=new Request(...t);return this.handleRequest({request:s,event:e})}));e.waitUntil(s),e.ports&&e.ports[0]&&s.then(()=>e.ports[0].postMessage(!0))}})}handleRequest({request:e,event:t}){const s=new URL(e.url,location.href);if(!s.protocol.startsWith("http"))return void 0;const n=s.origin===location.origin,{params:r,route:i}=this.findMatchingRoute({event:t,request:e,sameOrigin:n,url:s});let a=i&&i.handler;const o=e.method;if(!a&&this._defaultHandlerMap.has(o)&&(a=this._defaultHandlerMap.get(o)),!a)return void 0;let c;try{c=a.handle({url:s,request:e,event:t,params:r})}catch(e){c=Promise.reject(e)}const h=i&&i.catchHandler;return c instanceof Promise&&(this._catchHandler||h)&&(c=c.catch(async n=>{if(h){0;try{return await h.handle({url:s,request:e,event:t,params:r})}catch(e){n=e}}if(this._catchHandler)return this._catchHandler.handle({url:s,request:e,event:t});throw n})),c}findMatchingRoute({url:e,sameOrigin:t,request:s,event:n}){const r=this._routes.get(s.method)||[];for(const i of r){let r;const a=i.match({url:e,sameOrigin:t,request:s,event:n});if(a)return r=a,(Array.isArray(a)&&0===a.length||a.constructor===Object&&0===Object.keys(a).length||"boolean"==typeof a)&&(r=void 0),{route:i,params:r}}return{}}setDefaultHandler(e,t="GET"){this._defaultHandlerMap.set(t,H(e))}setCatchHandler(e){this._catchHandler=H(e)}registerRoute(e){this._routes.has(e.method)||this._routes.set(e.method,[]),this._routes.get(e.method).push(e)}unregisterRoute(e){if(!this._routes.has(e.method))throw new W("unregister-route-but-not-found-with-method",{method:e.method});const t=this._routes.get(e.method).indexOf(e);if(!(t>-1))throw new W("unregister-route-route-not-registered");this._routes.get(e.method).splice(t,1)}}let z;const V=()=>(z||(z=new J,z.addFetchListener(),z.addCacheListener()),z);s(2);const G=(e,...t)=>{let s=e;return t.length>0&&(s+=" :: "+JSON.stringify(t)),s};class Q extends Error{constructor(e,t){super(G(e,t)),this.name=e,this.details=t}}const $={googleAnalytics:"googleAnalytics",precache:"precache-v2",prefix:"workbox",runtime:"runtime",suffix:"undefined"!=typeof registration?registration.scope:""},Z=e=>[$.prefix,e,$.suffix].filter(e=>e&&e.length>0).join("-"),X=e=>e||Z($.runtime),Y=e=>new URL(String(e),location.href).href.replace(new RegExp("^"+location.origin),"");function ee(e,t){const s=new URL(e);for(const e of t)s.searchParams.delete(e);return s.href}class te{constructor(){this.promise=new Promise((e,t)=>{this.resolve=e,this.reject=t})}}const se=new Set;function ne(e){return new Promise(t=>setTimeout(t,e))}s(5);function re(e){return"string"==typeof e?new Request(e):e}class ie{constructor(e,t){this._cacheKeys={},Object.assign(this,t),this.event=t.event,this._strategy=e,this._handlerDeferred=new te,this._extendLifetimePromises=[],this._plugins=[...e.plugins],this._pluginStateMap=new Map;for(const e of this._plugins)this._pluginStateMap.set(e,{});this.event.waitUntil(this._handlerDeferred.promise)}async fetch(e){const{event:t}=this;let s=re(e);if("navigate"===s.mode&&t instanceof FetchEvent&&t.preloadResponse){const e=await t.preloadResponse;if(e)return e}const n=this.hasCallback("fetchDidFail")?s.clone():null;try{for(const e of this.iterateCallbacks("requestWillFetch"))s=await e({request:s.clone(),event:t})}catch(e){throw new Q("plugin-error-request-will-fetch",{thrownError:e})}const r=s.clone();try{let e;e=await fetch(s,"navigate"===s.mode?void 0:this._strategy.fetchOptions);for(const s of this.iterateCallbacks("fetchDidSucceed"))e=await s({event:t,request:r,response:e});return e}catch(e){throw n&&await this.runCallbacks("fetchDidFail",{error:e,event:t,originalRequest:n.clone(),request:r.clone()}),e}}async fetchAndCachePut(e){const t=await this.fetch(e),s=t.clone();return this.waitUntil(this.cachePut(e,s)),t}async cacheMatch(e){const t=re(e);let s;const{cacheName:n,matchOptions:r}=this._strategy,i=await this.getCacheKey(t,"read"),a={...r,cacheName:n};s=await caches.match(i,a);for(const e of this.iterateCallbacks("cachedResponseWillBeUsed"))s=await e({cacheName:n,matchOptions:r,cachedResponse:s,request:i,event:this.event})||void 0;return s}async cachePut(e,t){const s=re(e);await ne(0);const n=await this.getCacheKey(s,"write");if(!t)throw new Q("cache-put-with-no-response",{url:Y(n.url)});const r=await this._ensureResponseSafeToCache(t);if(!r)return!1;const{cacheName:i,matchOptions:a}=this._strategy,o=await self.caches.open(i),c=this.hasCallback("cacheDidUpdate"),h=c?await async function(e,t,s,n){const r=ee(t.url,s);if(t.url===r)return e.match(t,n);const i=Object.assign(Object.assign({},n),{ignoreSearch:!0}),a=await e.keys(t,i);for(const t of a){if(r===ee(t.url,s))return e.match(t,n)}}(o,n.clone(),["__WB_REVISION__"],a):null;try{await o.put(n,c?r.clone():r)}catch(e){throw"QuotaExceededError"===e.name&&await async function(){for(const e of se)await e()}(),e}for(const e of this.iterateCallbacks("cacheDidUpdate"))await e({cacheName:i,oldResponse:h,newResponse:r.clone(),request:n,event:this.event});return!0}async getCacheKey(e,t){if(!this._cacheKeys[t]){let s=e;for(const e of this.iterateCallbacks("cacheKeyWillBeUsed"))s=re(await e({mode:t,request:s,event:this.event,params:this.params}));this._cacheKeys[t]=s}return this._cacheKeys[t]}hasCallback(e){for(const t of this._strategy.plugins)if(e in t)return!0;return!1}async runCallbacks(e,t){for(const s of this.iterateCallbacks(e))await s(t)}*iterateCallbacks(e){for(const t of this._strategy.plugins)if("function"==typeof t[e]){const s=this._pluginStateMap.get(t),n=n=>{const r={...n,state:s};return t[e](r)};yield n}}waitUntil(e){return this._extendLifetimePromises.push(e),e}async doneWaiting(){let e;for(;e=this._extendLifetimePromises.shift();)await e}destroy(){this._handlerDeferred.resolve()}async _ensureResponseSafeToCache(e){let t=e,s=!1;for(const e of this.iterateCallbacks("cacheWillUpdate"))if(t=await e({request:this.request,response:t,event:this.event})||void 0,s=!0,!t)break;return s||t&&200!==t.status&&(t=void 0),t}}class ae{constructor(e={}){this.cacheName=X(e.cacheName),this.plugins=e.plugins||[],this.fetchOptions=e.fetchOptions,this.matchOptions=e.matchOptions}handle(e){const[t]=this.handleAll(e);return t}handleAll(e){e instanceof FetchEvent&&(e={event:e,request:e.request});const t=e.event,s="string"==typeof e.request?new Request(e.request):e.request,n="params"in e?e.params:void 0,r=new ie(this,{event:t,request:s,params:n}),i=this._getResponse(r,s,t);return[i,this._awaitComplete(i,r,s,t)]}async _getResponse(e,t,s){await e.runCallbacks("handlerWillStart",{event:s,request:t});let n=void 0;try{if(n=await this._handle(t,e),!n||"error"===n.type)throw new Q("no-response",{url:t.url})}catch(r){for(const i of e.iterateCallbacks("handlerDidError"))if(n=await i({error:r,event:s,request:t}),n)break;if(!n)throw r}for(const r of e.iterateCallbacks("handlerWillRespond"))n=await r({event:s,request:t,response:n});return n}async _awaitComplete(e,t,s,n){let r,i;try{r=await e}catch(i){}try{await t.runCallbacks("handlerDidRespond",{event:n,request:s,response:r}),await t.doneWaiting()}catch(e){i=e}if(await t.runCallbacks("handlerDidComplete",{event:n,request:s,response:r,error:i}),t.destroy(),i)throw i}}const oe={cacheWillUpdate:async({response:e})=>200===e.status||0===e.status?e:null};class ce extends ae{constructor(e){super(e),this.plugins.some(e=>"cacheWillUpdate"in e)||this.plugins.unshift(oe)}async _handle(e,t){const s=t.fetchAndCachePut(e).catch(()=>{});let n,r=await t.cacheMatch(e);if(r)0;else{0;try{r=await s}catch(e){n=e}}if(!r)throw new Q("no-response",{url:e.url,error:n});return r}}const he=a("Clinify-API-Cache","PostResponses"),le=new D({cacheName:"clinify-sw-cache",plugins:[new class{constructor(e={}){var t;this.cachedResponseWillBeUsed=async({event:e,request:t,cacheName:s,cachedResponse:n})=>{if(!n)return null;const r=this._isResponseDateFresh(n),i=this._getCacheExpiration(s);u(i.expireEntries());const a=i.updateTimestamp(t.url);if(e)try{e.waitUntil(a)}catch(e){0}return r?n:null},this.cacheDidUpdate=async({cacheName:e,request:t})=>{const s=this._getCacheExpiration(e);await s.updateTimestamp(t.url),await s.expireEntries()},this._config=e,this._maxAgeSeconds=e.maxAgeSeconds,this._cacheExpirations=new Map,e.purgeOnQuotaError&&(t=()=>this.deleteCacheAndMetadata(),v.add(t))}_getCacheExpiration(e){if(e===_())throw new l("expire-custom-caches-only");let t=this._cacheExpirations.get(e);return t||(t=new w(e,this._config),this._cacheExpirations.set(e,t)),t}_isResponseDateFresh(e){if(!this._maxAgeSeconds)return!0;const t=this._getDateHeaderTimestamp(e);if(null===t)return!0;return t>=Date.now()-1e3*this._maxAgeSeconds}_getDateHeaderTimestamp(e){if(!e.headers.has("date"))return null;const t=e.headers.get("date"),s=new Date(t).getTime();return isNaN(s)?null:s}async deleteCacheAndMetadata(){for(const[e,t]of this._cacheExpirations)await self.caches.delete(e),await t.delete();this._cacheExpirations=new Map}}({maxAgeSeconds:86400,maxEntries:200})]});async function ue(e){const t={};for(const s of e.headers.entries())t[s[0]]=s[1];const s={headers:t,status:e.status,statusText:e.statusText};return s.body=await e.json(),s}!function(e,t,s){let n;if("string"==typeof e){const r=new URL(e,location.href);0;n=new I(({url:e})=>e.href===r.href,t,s)}else if(e instanceof RegExp)n=new F(e,t,s);else if("function"==typeof e)n=new I(e,t,s);else{if(!(e instanceof I))throw new W("unsupported-route-type",{moduleName:"workbox-routing",funcName:"registerRoute",paramName:"capture"});n=e}V().registerRoute(n)}(new RegExp("/graphql(/)?"),async e=>{let{event:t}=e;return async function(e){const t=await async function(e){let t;try{const s=await e.json(),n=r()(s.query).toString();if(t=await function(e,t=c()){return t("readonly",t=>i(t.get(e)))}(n,he),!t)return null;const a=e.headers.get("Cache-Control"),o=a?parseInt(a.split("=")[1]):3600;return Date.now()-t.timestamp>1e3*o?null:new Response(JSON.stringify(t.response.body),t.response)}catch(e){return null}}(e.request.clone());return fetch(e.request.clone()).then(s=>(async function(e,t){const s=await e.json(),n=r()(s.query).toString(),a={query:s.query,response:await ue(t),timestamp:Date.now()};!function(e,t,s=c()){s("readwrite",s=>(s.put(t,e),i(s.transaction)))}(n,a,he)}(e.request.clone(),s.clone()),s.ok?s:t||s)).catch(()=>Promise.resolve(t))||Promise.resolve(t)}(t)},"POST"),le.addToCacheList([{url:"/index.html",revision:"001"},{url:"/manifest.json",revision:"001"},{url:"/css/bootstrap.css",revision:null},{url:"/css/mdb.min.css",revision:null},{url:"/css/styles.css",revision:"001"},{url:"/fonts/Lato-Bold.ttf",revision:null},{url:"/fonts/Lato-Regular.ttf",revision:null},{url:"/images/camera.png",revision:null},{url:"/images/profile-image.png",revision:null},{url:"/js/bootstrap.js",revision:null},{url:"/js/jquery-3.4.1.min.js",revision:null},{url:"/js/popper.min.js",revision:null},{url:"/icons/manifest-icon-192.png",revision:"001"},{url:"/icons/manifest-icon-512.png",revision:"001"},{url:"https://fonts.googleapis.com/css2?family=Lato:wght@300;400;500;600;700&display=swap",revision:null},{url:"https://fonts.gstatic.com/s/poppins/v15/pxiByp8kv8JHgFVrLGT9Z1xlFd2JQEk.woff2",revision:null},{url:"https://fonts.gstatic.com/s/poppins/v15/pxiEyp8kv8JHgFVrJJfecnFHGPc.woff2",revision:null},{url:"https://fonts.gstatic.com/s/poppins/v15/pxiByp8kv8JHgFVrLEj6Z1xlFd2JQEk.woff2",revision:null},{url:"/icons/favicon.ico",revision:null}]),self.addEventListener("fetch",async e=>{const{request:t}=e;if(new URL(t.url).origin===location.origin){const s=le.getCacheKeyForURL("".concat(location.origin,"/index.html"));return e.respondWith(new ce({cacheName:"clinify-sw-cache"}).handle({event:e,request:t}).catch(()=>caches.match(s)))}0===e.request.url.indexOf("http")&&"GET"===t.method&&e.respondWith(new ce({cacheName:"clinify-sw-cache"}).handle({event:e,request:t}))}),self.addEventListener("install",e=>{e.waitUntil((async()=>(await le.install(e),self.skipWaiting()))())}),self.addEventListener("activate",e=>{e.waitUntil((async()=>(await le.activate(e),self.clients.claim()))())})}]);